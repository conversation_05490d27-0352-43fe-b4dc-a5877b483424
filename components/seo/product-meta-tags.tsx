import Head from "next/head";
import { Product } from "@/utils/types";

interface ProductMetaTagsProps {
  product: Product;
  effectivePrice: number;
  isOnSale: boolean;
}

export default function ProductMetaTags({ 
  product, 
  effectivePrice, 
  isOnSale 
}: ProductMetaTagsProps) {
  const formatPrice = (price: number) => `M ${price.toFixed(2)}`;
  const productUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://rivvsneakers.shop'}/products/${product.id}`;
  const productImage = product.images && product.images.length > 0 ? product.images[0] : '';
  
  const title = `${product.name} - RIVV Sneakers`;
  const description = `${product.name} by ${product.brand}. ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)})` : ''}. ⭐ ${product.rating.toFixed(1)}/5 rating. ${product.stock > 0 ? 'In Stock' : 'Out of Stock'}. Premium quality footwear from RIVV Sneakers.`;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={`${product.name}, ${product.brand}, sneakers, footwear, RIVV, Lesotho, premium shoes, ${product.category?.name || 'shoes'}`} />
      
      {/* Open Graph Meta Tags for Facebook */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={productImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={`${product.name} - Premium sneakers from RIVV`} />
      <meta property="og:url" content={productUrl} />
      <meta property="og:type" content="product" />
      <meta property="og:site_name" content="RIVV Premium Sneakers" />
      <meta property="og:locale" content="en_LS" />
      
      {/* Product-specific Open Graph */}
      <meta property="product:brand" content={product.brand} />
      <meta property="product:availability" content={product.stock > 0 ? "in stock" : "out of stock"} />
      <meta property="product:condition" content="new" />
      <meta property="product:price:amount" content={effectivePrice.toString()} />
      <meta property="product:price:currency" content="LSL" />
      <meta property="product:retailer_item_id" content={product.id} />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={productImage} />
      <meta name="twitter:image:alt" content={`${product.name} - Premium sneakers from RIVV`} />
      
      {/* WhatsApp specific meta tags */}
      <meta property="og:image:type" content="image/jpeg" />
      <meta property="og:image:secure_url" content={productImage} />
      
      {/* Additional meta for better sharing */}
      <meta name="author" content="RIVV Premium Sneakers" />
      <meta name="robots" content="index, follow" />
      <link rel="canonical" href={productUrl} />
      
      {/* Schema.org structured data for better SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Product",
            "name": product.name,
            "image": product.images,
            "description": product.description || description,
            "brand": {
              "@type": "Brand",
              "name": product.brand
            },
            "offers": {
              "@type": "Offer",
              "url": productUrl,
              "priceCurrency": "LSL",
              "price": effectivePrice,
              "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
              "itemCondition": "https://schema.org/NewCondition",
              "availability": product.stock > 0 ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
              "seller": {
                "@type": "Organization",
                "name": "RIVV Premium Sneakers"
              }
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": product.rating,
              "reviewCount": product.reviewCount,
              "bestRating": 5,
              "worstRating": 1
            },
            "category": product.category?.name || "Footwear",
            "sku": product.id,
            "mpn": product.id
          })
        }}
      />
    </Head>
  );
}
