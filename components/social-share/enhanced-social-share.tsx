"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Share2, Facebook, Instagram, Download, Copy } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Product } from "@/utils/types";

interface EnhancedSocialShareProps {
  product: Product;
  effectivePrice: number;
  isOnSale: boolean;
}

export default function EnhancedSocialShare({ 
  product, 
  effectivePrice, 
  isOnSale 
}: EnhancedSocialShareProps) {
  const { toast } = useToast();
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);

  const formatPrice = (price: number) => `M ${price.toFixed(2)}`;

  // Helper function to detect mobile and auto-download image
  const isMobileDevice = () => /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  const autoDownloadForMobile = async (blob: Blob, filename: string) => {
    if (isMobileDevice()) {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      return true;
    }
    return false;
  };

  // Generate a mobile-optimized shareable image with proper responsiveness
  const generateShareableImage = async (): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;

      // Detect mobile and set appropriate canvas size
      const isMobile = window.innerWidth <= 768;
      const devicePixelRatio = window.devicePixelRatio || 1;

      // Mobile-optimized dimensions (Instagram story format for mobile, landscape for desktop)
      const canvasWidth = isMobile ? 1080 : 1200;
      const canvasHeight = isMobile ? 2160 : 800; // Increased height for mobile to accommodate larger text

      // Set canvas size with device pixel ratio for crisp images
      canvas.width = canvasWidth * devicePixelRatio;
      canvas.height = canvasHeight * devicePixelRatio;
      canvas.style.width = canvasWidth + 'px';
      canvas.style.height = canvasHeight + 'px';

      // Scale context to match device pixel ratio
      ctx.scale(devicePixelRatio, devicePixelRatio);

      // Background gradient
      const gradient = ctx.createLinearGradient(0, 0, canvasWidth, canvasHeight);
      gradient.addColorStop(0, '#f8fafc');
      gradient.addColorStop(1, '#e2e8f0');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      // Load product image first
      const img = new Image();
      img.crossOrigin = 'anonymous';

      // Load RIVV logo
      const logo = new Image();
      logo.crossOrigin = 'anonymous';

      let imagesLoaded = 0;
      const totalImages = 2;

      const checkAllImagesLoaded = () => {
        imagesLoaded++;
        if (imagesLoaded === totalImages) {
          drawCanvas();
        }
      };

      const drawCanvas = () => {
        if (isMobile) {
          // Mobile layout (vertical/story format)
          drawMobileLayout();
        } else {
          // Desktop layout (horizontal format)
          drawDesktopLayout();
        }

        // Convert canvas to blob and create URL
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            resolve(url);
          }
        }, 'image/png', 0.9);
      };

      const drawMobileLayout = () => {
        const padding = 50;
        const logoSize = 80;

        // Add RIVV logo at top
        const logoX = (canvasWidth - logoSize) / 2;
        const logoY = padding;
        ctx.drawImage(logo, logoX, logoY, logoSize, logoSize);

        // Product image (maintain aspect ratio)
        const maxImgWidth = canvasWidth - (padding * 2);
        const maxImgHeight = canvasHeight * 0.35; // 35% of canvas height to leave more room for text

        let imgWidth = maxImgWidth;
        let imgHeight = maxImgWidth; // Start with square

        // Calculate proper dimensions maintaining aspect ratio
        if (img.naturalWidth && img.naturalHeight) {
          const aspectRatio = img.naturalWidth / img.naturalHeight;
          if (aspectRatio > 1) {
            // Landscape image
            imgHeight = imgWidth / aspectRatio;
          } else {
            // Portrait image
            imgWidth = imgHeight * aspectRatio;
          }

          // Ensure it fits within max dimensions
          if (imgHeight > maxImgHeight) {
            imgHeight = maxImgHeight;
            imgWidth = imgHeight * aspectRatio;
          }
        }

        const imgX = (canvasWidth - imgWidth) / 2;
        const imgY = logoY + logoSize + padding;

        // Create rounded rectangle for image with proper aspect ratio
        ctx.save();
        ctx.beginPath();
        ctx.roundRect(imgX, imgY, imgWidth, imgHeight, 20);
        ctx.clip();
        ctx.drawImage(img, imgX, imgY, imgWidth, imgHeight);
        ctx.restore();

        // Text area below image with more spacing
        const textStartY = imgY + imgHeight + (padding * 1.5);
        drawProductInfo(padding, textStartY, canvasWidth - (padding * 2), true);
      };

      const drawDesktopLayout = () => {
        // Desktop layout (original horizontal format with improvements)
        const imgSize = 400;
        const imgX = 50;
        const imgY = (canvasHeight - imgSize) / 2;

        // Create rounded rectangle for image with proper aspect ratio
        ctx.save();
        ctx.beginPath();
        ctx.roundRect(imgX, imgY, imgSize, imgSize, 20);
        ctx.clip();

        // Maintain aspect ratio for desktop too
        if (img.naturalWidth && img.naturalHeight) {
          const aspectRatio = img.naturalWidth / img.naturalHeight;
          let drawWidth = imgSize;
          let drawHeight = imgSize;
          let drawX = imgX;
          let drawY = imgY;

          if (aspectRatio > 1) {
            // Landscape - fit width, center height
            drawHeight = imgSize / aspectRatio;
            drawY = imgY + (imgSize - drawHeight) / 2;
          } else {
            // Portrait - fit height, center width
            drawWidth = imgSize * aspectRatio;
            drawX = imgX + (imgSize - drawWidth) / 2;
          }

          ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);
        } else {
          ctx.drawImage(img, imgX, imgY, imgSize, imgSize);
        }
        ctx.restore();

        // Add white overlay for text area
        ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
        ctx.beginPath();
        ctx.roundRect(500, 50, 650, 700, 20);
        ctx.fill();

        // Add RIVV logo at top right corner
        const logoSize = 80;
        const logoX = canvasWidth - logoSize - 30;
        const logoY = 30;
        ctx.drawImage(logo, logoX, logoY, logoSize, logoSize);

        // Desktop text area
        drawProductInfo(540, 120, 610, false);
      };

      const drawProductInfo = (startX: number, startY: number, maxWidth: number, isMobileLayout: boolean) => {
        let currentY = startY;
        const lineHeight = isMobileLayout ? 55 : 35;
        const titleSize = isMobileLayout ? 42 : 28;
        const textSize = isMobileLayout ? 32 : 20;
        const smallTextSize = isMobileLayout ? 28 : 16;

        // Add RIVV text brand (responsive sizing)
        ctx.fillStyle = '#1e40af';
        ctx.font = `bold ${isMobileLayout ? 40 : 48}px Arial`;
        ctx.fillText('RIVV', startX, currentY);
        currentY += isMobileLayout ? 35 : 30;

        ctx.fillStyle = '#64748b';
        ctx.font = `${textSize}px Arial`;
        ctx.fillText('Premium Sneakers', startX, currentY);
        currentY += isMobileLayout ? 50 : 50;

        // Product name with proper text wrapping
        ctx.fillStyle = '#1f2937';
        ctx.font = `bold ${titleSize}px Arial`;
        const words = product.name.split(' ');
        let line = '';
        let lineCount = 0;
        const maxLines = isMobileLayout ? 3 : 2; // More lines for mobile

        for (let n = 0; n < words.length && lineCount < maxLines; n++) {
          const testLine = line + words[n] + ' ';
          const metrics = ctx.measureText(testLine);
          const testWidth = metrics.width;

          if (testWidth > maxWidth && n > 0) {
            ctx.fillText(line.trim(), startX, currentY);
            line = words[n] + ' ';
            currentY += lineHeight;
            lineCount++;
          } else {
            line = testLine;
          }
        }

        // Handle remaining text
        if (line.trim() && lineCount < maxLines) {
          // If we're on the last allowed line and there are more words, add ellipsis
          if (lineCount === maxLines - 1 && words.length > words.indexOf(line.trim().split(' ').pop() || '') + 1) {
            line = line.trim() + '...';
          }
          ctx.fillText(line.trim(), startX, currentY);
        }
        currentY += isMobileLayout ? 60 : 50;

        // Brand
        ctx.fillStyle = '#374151';
        ctx.font = `bold ${textSize}px Arial`;
        ctx.fillText(`Brand: ${product.brand}`, startX, currentY);
        currentY += lineHeight;

        // Price
        ctx.fillStyle = '#dc2626';
        ctx.font = `bold ${isMobileLayout ? 32 : 36}px Arial`;
        ctx.fillText(`Price: ${formatPrice(effectivePrice)}`, startX, currentY);
        currentY += lineHeight;

        if (isOnSale) {
          ctx.fillStyle = '#9ca3af';
          ctx.font = `${textSize}px Arial`;
          ctx.save();
          ctx.setLineDash([2, 2]);
          ctx.strokeStyle = '#9ca3af';
          ctx.lineWidth = 2;
          const originalPriceText = `Was: ${formatPrice(product.price)}`;
          ctx.fillText(originalPriceText, startX, currentY);

          // Draw strikethrough line
          const originalPriceWidth = ctx.measureText(originalPriceText).width;
          const strikeY = currentY - 8;
          ctx.beginPath();
          ctx.moveTo(startX, strikeY);
          ctx.lineTo(startX + originalPriceWidth, strikeY);
          ctx.stroke();
          ctx.restore();
          currentY += lineHeight - 10;
        } else {
          currentY += 15;
        }

        // Sizes Available
        if (product.sizes && product.sizes.length > 0) {
          ctx.fillStyle = '#374151';
          ctx.font = `${smallTextSize}px Arial`;
          const maxSizes = isMobileLayout ? 6 : 8;
          const sizesText = `Sizes Available: ${product.sizes.slice(0, maxSizes).join(', ')}${product.sizes.length > maxSizes ? '...' : ''}`;
          ctx.fillText(sizesText, startX, currentY);
          currentY += lineHeight;
        }

        // Stock Status
        ctx.fillStyle = product.stock > 0 ? '#059669' : '#dc2626';
        ctx.font = `bold ${textSize}px Arial`;
        ctx.fillText(`Stock: ${product.stock > 0 ? 'In Stock' : 'Out of Stock'}`, startX, currentY);
        currentY += lineHeight;

        // Rating
        ctx.fillStyle = '#f59e0b';
        ctx.font = `${smallTextSize}px Arial`;
        ctx.fillText(`⭐ ${product.rating.toFixed(1)}/5 (${product.reviewCount} reviews)`, startX, currentY);
        currentY += lineHeight;

        // Description with improved text wrapping
        if (product.description) {
          ctx.fillStyle = '#4b5563';
          ctx.font = `${smallTextSize - 2}px Arial`;

          // Split description into sentences and take first two
          const sentences = product.description.split(/[.!?]+/).filter(s => s.trim().length > 0);
          const firstTwoSentences = sentences.slice(0, 2).join('. ') + (sentences.length > 0 ? '.' : '');

          // Wrap text for description with better handling
          const descWords = firstTwoSentences.split(' ');
          let descLine = '';
          const descMaxWidth = maxWidth - 20; // Use responsive width
          let lineCount = 0;
          const maxLines = isMobileLayout ? 4 : 3;

          for (let n = 0; n < descWords.length && lineCount < maxLines; n++) {
            const testLine = descLine + descWords[n] + ' ';
            const metrics = ctx.measureText(testLine);
            const testWidth = metrics.width;

            if (testWidth > descMaxWidth && n > 0) {
              ctx.fillText(descLine.trim(), startX, currentY);
              descLine = descWords[n] + ' ';
              currentY += lineHeight - 10;
              lineCount++;
            } else {
              descLine = testLine;
            }
          }

          // Handle the last line
          if (descLine.trim() && lineCount < maxLines) {
            // If we're on the last allowed line and there are more words, add ellipsis
            const remainingWords = descWords.slice(descWords.findIndex(word =>
              descLine.includes(word)) + descLine.trim().split(' ').length);

            if (lineCount === maxLines - 1 && remainingWords.length > 0) {
              // Ensure the line fits with ellipsis
              let finalLine = descLine.trim();
              while (ctx.measureText(finalLine + '...').width > descMaxWidth && finalLine.length > 0) {
                finalLine = finalLine.substring(0, finalLine.lastIndexOf(' '));
              }
              finalLine += '...';
              ctx.fillText(finalLine, startX, currentY);
            } else {
              ctx.fillText(descLine.trim(), startX, currentY);
            }
            currentY += lineHeight;
          }
        }

        // Website URL
        ctx.fillStyle = '#6366f1';
        ctx.font = `bold ${smallTextSize}px Arial`;
        ctx.fillText('🛒 Shop at rivvsneakers.shop', startX, currentY + 20);
      };

      // Set up image loading with better error handling
      img.onload = checkAllImagesLoaded;
      img.onerror = () => {
        console.warn('Product image failed to load, continuing without it');
        checkAllImagesLoaded();
      };

      logo.onload = checkAllImagesLoaded;
      logo.onerror = () => {
        console.warn('Logo failed to load, continuing without it');
        checkAllImagesLoaded();
      };

      // Load images with CORS handling
      if (product.images && product.images.length > 0) {
        // Try to load the image with CORS first, fallback to no CORS
        const imageUrl = product.images[0];

        // Create a test image to check if CORS is needed
        const testImg = new Image();
        testImg.onload = () => {
          img.src = imageUrl;
        };
        testImg.onerror = () => {
          // If CORS fails, try without crossOrigin
          img.crossOrigin = '';
          img.src = imageUrl;
        };
        testImg.crossOrigin = 'anonymous';
        testImg.src = imageUrl;
      } else {
        img.onerror?.(new Event('error'));
      }

      // Load RIVV logo from public folder
      logo.src = '/logo.png';
    });
  };

  // Enhanced WhatsApp share with mobile-optimized image
  const handleWhatsAppShare = async () => {
    setIsGeneratingImage(true);

    try {
      // Generate mobile-optimized image
      const imageUrl = await generateShareableImage();
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      // Create a more descriptive filename
      const timestamp = new Date().toISOString().slice(0, 10);
      const cleanName = product.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
      const filename = `RIVV_${cleanName}_${timestamp}.png`;
      const file = new File([blob], filename, { type: 'image/png' });

      // Auto-download for mobile devices
      const wasDownloaded = await autoDownloadForMobile(blob, filename);

      // Try Web Share API first (better for mobile)
      if (navigator.share && navigator.canShare) {
        const shareData = {
          title: `${product.name} - RIVV Sneakers`,
          text: `🔥 Check out this amazing product from RIVV Sneakers!\n\n${product.name}\n💰 ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)}) 🔥` : ''}\n⭐ ${product.rating.toFixed(1)}/5 rating\n${product.stock > 0 ? '✅ In Stock' : '❌ Out of Stock'}\n\n🛒 Shop now at rivvsneakers.shop\n\n#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho`,
          files: [file]
        };

        if (navigator.canShare(shareData)) {
          await navigator.share(shareData);
          toast({
            title: "Shared successfully!",
            description: wasDownloaded ? "Product shared with image downloaded for attachment." : "Product shared with high-quality image attached.",
          });
          URL.revokeObjectURL(imageUrl);
          return;
        }
      }

      // Fallback: Open WhatsApp with message
      const productUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://rivvsneakers.shop'}/products/${product.id}`;
      const message = `*🔥 Check out this amazing product from RIVV Sneakers!*

${product.name}

💰 ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)}) 🔥` : ''}
⭐ ${product.rating.toFixed(1)}/5 rating
${product.stock > 0 ? '✅ In Stock' : '❌ Out of Stock'}

🛒 Shop now: ${productUrl}

#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho`;

      const encodedMessage = encodeURIComponent(message);
      const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;

      // Small delay to ensure download completes on mobile
      setTimeout(() => {
        window.open(whatsappUrl, '_blank');
      }, wasDownloaded ? 800 : 100);

      toast({
        title: wasDownloaded ? "Image downloaded & WhatsApp opened!" : "Opening WhatsApp",
        description: wasDownloaded ? `Image "${filename}" saved to your device. Attach it to your WhatsApp message!` : "Product details ready to share. Download the image separately if needed.",
        duration: wasDownloaded ? 6000 : 4000,
      });

      URL.revokeObjectURL(imageUrl);

    } catch (error) {
      console.error('Error sharing:', error);
      toast({
        title: "Sharing failed",
        description: "Please try again or share manually.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Enhanced Facebook share with better image handling
  const handleFacebookShare = async () => {
    setIsGeneratingImage(true);

    try {
      const productUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://rivvsneakers.shop'}/products/${product.id}`;

      // Generate image and auto-download for mobile
      const imageUrl = await generateShareableImage();
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const timestamp = new Date().toISOString().slice(0, 10);
      const cleanName = product.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
      const filename = `RIVV_${cleanName}_${timestamp}.png`;
      const file = new File([blob], filename, { type: 'image/png' });

      // Auto-download for mobile devices
      const wasDownloaded = await autoDownloadForMobile(blob, filename);

      // Try Web Share API first (works on mobile)
      if (navigator.share && navigator.canShare) {
        const shareData = {
          title: `${product.name} - RIVV Sneakers`,
          text: `🔥 Check out this amazing product from RIVV Sneakers!\n\n${product.name}\nBrand: ${product.brand}\n💰 ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)}) 🔥` : ''}\n⭐ ${product.rating.toFixed(1)}/5 rating\n${product.stock > 0 ? '✅ In Stock' : '❌ Out of Stock'}\n\n🛒 Shop now: ${productUrl}\n\n#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho`,
          files: [file]
        };

        if (navigator.canShare(shareData)) {
          await navigator.share(shareData);
          toast({
            title: "Shared successfully!",
            description: wasDownloaded ? "Product shared with image downloaded for attachment." : "Product shared to Facebook with image attached.",
          });
          URL.revokeObjectURL(imageUrl);
          return;
        }
      }

      // Fallback: Use Facebook's sharing URL with better integration
      const shareText = `🔥 Check out this amazing product from RIVV Sneakers!

${product.name}
Brand: ${product.brand}
💰 ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)}) 🔥` : ''}
⭐ ${product.rating.toFixed(1)}/5 rating
${product.stock > 0 ? '✅ In Stock' : '❌ Out of Stock'}

#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho`;

      // Use Facebook's sharer URL for better integration
      const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(productUrl)}&quote=${encodeURIComponent(shareText)}`;

      // Small delay to ensure download completes on mobile
      setTimeout(() => {
        window.open(
          facebookUrl,
          'facebook-share-dialog',
          'width=626,height=436,resizable=yes,scrollbars=yes'
        );
      }, wasDownloaded ? 800 : 100);

      // For desktop users, also download the image
      if (!wasDownloaded) {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      URL.revokeObjectURL(imageUrl);

      toast({
        title: "Facebook sharing opened!",
        description: `Image "${filename}" ${wasDownloaded ? 'saved to your device' : 'downloaded'}. Attach it to your Facebook post!`,
        duration: 5000,
      });

    } catch (error) {
      console.error('Error sharing to Facebook:', error);
      toast({
        title: "Facebook sharing failed",
        description: "Please try again or share manually.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Enhanced Instagram share with better image handling
  const handleInstagramShare = async () => {
    setIsGeneratingImage(true);

    try {
      const productUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://rivvsneakers.shop'}/products/${product.id}`;

      // Generate image and auto-download for mobile
      const imageUrl = await generateShareableImage();
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const timestamp = new Date().toISOString().slice(0, 10);
      const cleanName = product.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
      const filename = `RIVV_${cleanName}_${timestamp}.png`;
      const file = new File([blob], filename, { type: 'image/png' });

      // Auto-download for mobile devices
      const wasDownloaded = await autoDownloadForMobile(blob, filename);

      // Try Web Share API first (works on mobile)
      if (navigator.share && navigator.canShare) {
        const shareData = {
          title: `${product.name} - RIVV Sneakers`,
          text: `${product.name} 👟\n\nBrand: ${product.brand}\n💰 ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)}) 🔥` : ''}\n⭐ ${product.rating.toFixed(1)}/5 rating\n${product.stock > 0 ? '✅ In Stock' : '❌ Out of Stock'}\n\n🛒 Shop: ${productUrl}\n\n#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho #Sneakers`,
          files: [file]
        };

        if (navigator.canShare(shareData)) {
          await navigator.share(shareData);
          toast({
            title: "Shared successfully!",
            description: wasDownloaded ? "Product shared with image downloaded for attachment." : "Product shared to Instagram with image attached.",
          });
          URL.revokeObjectURL(imageUrl);
          return;
        }
      }

      // For desktop users, also download the image
      if (!wasDownloaded) {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
      URL.revokeObjectURL(imageUrl);

      // Create Instagram-optimized caption (shorter, more hashtags)
      const caption = `${product.name} 👟

Brand: ${product.brand}
💰 ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)}) 🔥` : ''}
${product.sizes && product.sizes.length > 0 ? `Sizes: ${product.sizes.slice(0, 5).join(', ')}${product.sizes.length > 5 ? '...' : ''}` : ''}
⭐ ${product.rating.toFixed(1)}/5 rating
${product.stock > 0 ? '✅ In Stock' : '❌ Out of Stock'}

🛒 Shop: ${productUrl}

#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho #Sneakers #Fashion #Style #Footwear #${product.brand.replace(/\s+/g, '')} #PremiumSneakers #SneakerHead #FootwearFashion #QualityShoes`;

      await navigator.clipboard.writeText(caption);

      toast({
        title: "Instagram ready!",
        description: `Image "${filename}" ${wasDownloaded ? 'saved to your device' : 'downloaded'} and caption copied. Opening Instagram...`,
        duration: 4000,
      });

      // Small delay to ensure download completes on mobile
      setTimeout(() => {
        window.open('https://www.instagram.com/rivv_premium_sneakers01/', '_blank');
      }, wasDownloaded ? 800 : 100);

    } catch (error) {
      console.error('Error preparing Instagram share:', error);
      toast({
        title: "Instagram sharing failed",
        description: "Please try again or share manually.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Download shareable image with enhanced handling
  const handleDownloadImage = async () => {
    setIsGeneratingImage(true);

    try {
      const imageUrl = await generateShareableImage();

      // Convert to blob for better handling
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      // Create descriptive filename
      const timestamp = new Date().toISOString().slice(0, 10);
      const cleanName = product.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
      const filename = `RIVV_${cleanName}_${timestamp}.png`;

      // Download the image
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL
      URL.revokeObjectURL(imageUrl);

      toast({
        title: "Image downloaded!",
        description: `"${filename}" saved. Ready to share on any platform!`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error downloading image:', error);
      toast({
        title: "Download failed",
        description: "Please try again or check your browser settings.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  return (
    <div className="space-y-3">
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
        <Button
          variant="outline"
          size="sm"
          className="bg-green-50 hover:bg-green-100 border-green-300 text-green-700 hover:text-green-800 transition-all duration-200 hover:shadow-md text-xs sm:text-sm"
          onClick={handleWhatsAppShare}
          disabled={isGeneratingImage}
        >
          {isGeneratingImage ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-1"></div>
          ) : (
            <Share2 className="h-4 w-4 mr-1" />
          )}
          WhatsApp
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700 hover:text-blue-800 transition-all duration-200 hover:shadow-md text-xs sm:text-sm"
          onClick={handleFacebookShare}
          disabled={isGeneratingImage}
        >
          {isGeneratingImage ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-1"></div>
          ) : (
            <Facebook className="h-4 w-4 mr-1" />
          )}
          Facebook
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="bg-pink-50 hover:bg-pink-100 border-pink-300 text-pink-700 hover:text-pink-800 transition-all duration-200 hover:shadow-md text-xs sm:text-sm"
          onClick={handleInstagramShare}
          disabled={isGeneratingImage}
        >
          {isGeneratingImage ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-pink-600 mr-1"></div>
          ) : (
            <Instagram className="h-4 w-4 mr-1" />
          )}
          Instagram
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="bg-gray-50 hover:bg-gray-100 border-gray-300 text-gray-700 hover:text-gray-800 transition-all duration-200 hover:shadow-md text-xs sm:text-sm"
          onClick={handleDownloadImage}
          disabled={isGeneratingImage}
        >
          {isGeneratingImage ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-1"></div>
          ) : (
            <Download className="h-4 w-4 mr-1" />
          )}
          Download
        </Button>
      </div>

      <div className="text-xs sm:text-sm text-gray-600 bg-blue-50 p-2 rounded-lg">
        <p className="font-medium text-blue-800 mb-1">📱 Mobile Sharing Improved!</p>
        <p>Images are now optimized for your device and automatically downloaded for easy attachment to your messages.</p>
      </div>
    </div>
  );
}
