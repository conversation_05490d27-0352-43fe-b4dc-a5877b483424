"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, <PERSON>, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Notification {
  id: string;
  title: string;
  content: string;
  type: "INFO" | "WARNING" | "SUCCESS" | "ERROR";
  priority: number;
  createdAt: string;
  isRead: boolean;
}

export default function NotificationPopup() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [expandedNotifications, setExpandedNotifications] = useState<Set<string>>(new Set());
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  // Fetch notifications on component mount and periodically
  useEffect(() => {
    fetchNotifications();

    // Only set up interval if user is authenticated
    let interval: NodeJS.Timeout | null = null;
    if (isAuthenticated !== false) {
      interval = setInterval(fetchNotifications, 30000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isAuthenticated]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/notices", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Include cookies for authentication
      });

      if (!response.ok) {
        // Handle different error types
        if (response.status === 401) {
          // User not authenticated - this is normal for non-logged-in users
          setIsAuthenticated(false);
          setNotifications([]);
          return;
        }
        if (response.status === 404) {
          // API endpoint not found - silently handle
          setIsAuthenticated(false);
          setNotifications([]);
          return;
        }
        console.error(`HTTP error! status: ${response.status}`);
        setNotifications([]);
        return;
      }

      const data = await response.json();

      if (data.success) {
        // User is authenticated
        setIsAuthenticated(true);
        // Only show unread notifications
        const unreadNotifications = data.data.filter((n: Notification) => !n.isRead);
        setNotifications(unreadNotifications);
      } else {
        console.error("API error:", data.error);
        setNotifications([]);
      }
    } catch (error) {
      // Silently handle network errors for notifications
      console.warn("Notifications unavailable:", error);
      setIsAuthenticated(false);
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await fetch("/api/notices", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ noticeId: notificationId }),
      });

      // Remove the notification from the list
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      // Also remove from expanded set
      setExpandedNotifications(prev => {
        const newSet = new Set(prev);
        newSet.delete(notificationId);
        return newSet;
      });
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  const toggleExpanded = (notificationId: string) => {
    setExpandedNotifications(prev => {
      const newSet = new Set(prev);
      if (newSet.has(notificationId)) {
        newSet.delete(notificationId);
      } else {
        newSet.add(notificationId);
      }
      return newSet;
    });
  };

  const isLongMessage = (content: string) => {
    // Check if message is longer than 3 lines (approximately 150 characters)
    return content.length > 150 || content.split('\n').length > 3;
  };

  const getTruncatedContent = (content: string) => {
    if (content.length <= 150) return content;
    return content.substring(0, 150) + '...';
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "SUCCESS": return "bg-green-500";
      case "WARNING": return "bg-yellow-500";
      case "ERROR": return "bg-red-500";
      default: return "bg-blue-500";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "SUCCESS": return "✅";
      case "WARNING": return "⚠️";
      case "ERROR": return "❌";
      default: return "ℹ️";
    }
  };

  // Don't render anything if no notifications
  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-[9999] max-w-sm w-full space-y-3 pointer-events-none">
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ 
              type: "spring", 
              stiffness: 300, 
              damping: 30,
              duration: 0.3 
            }}
            className="pointer-events-auto"
          >
            <Card className="shadow-lg border-l-4 border-l-green-500 bg-green-600 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-start justify-between gap-3">
                  <div className="flex items-start gap-3 flex-1">
                    {/* Type Icon */}
                    <div className={`w-8 h-8 rounded-full bg-green-700 flex items-center justify-center text-white text-sm flex-shrink-0 mt-0.5`}>
                      {getTypeIcon(notification.type)}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-white text-sm mb-1">
                        {notification.title}
                      </h4>
                      <div className="text-white text-sm leading-relaxed mb-2">
                        {isLongMessage(notification.content) && !expandedNotifications.has(notification.id) ? (
                          <>
                            <p>{getTruncatedContent(notification.content)}</p>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-green-200 hover:text-white hover:bg-green-700 p-0 h-auto mt-1 text-xs"
                              onClick={() => toggleExpanded(notification.id)}
                            >
                              Read More
                            </Button>
                          </>
                        ) : (
                          <p>{notification.content}</p>
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-green-200">
                          {new Date(notification.createdAt).toLocaleString()}
                        </span>
                        <Badge variant="outline" className="text-xs border-green-400 text-green-200">
                          Priority {notification.priority}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Close Button */}
                  <div className="flex flex-col gap-1 flex-shrink-0">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0 hover:bg-green-700 text-white"
                      onClick={() => markAsRead(notification.id)}
                      title="Close notification"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Mark as Read Button - Only show if expanded or short message */}
                {(!isLongMessage(notification.content) || expandedNotifications.has(notification.id)) && (
                  <div className="mt-3 pt-3 border-t border-green-500">
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full text-xs border-green-400 text-green-200 hover:bg-green-700 hover:text-white"
                      onClick={() => markAsRead(notification.id)}
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Mark as Read
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}
