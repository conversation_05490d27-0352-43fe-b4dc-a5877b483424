"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  CreditCard,
  Clock,
  Package,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Eye,
  RefreshCw,
} from "lucide-react";
import Link from "next/link";
import { formatPrice } from "@/lib/product-utils";
import {
  calculateDaysRemaining,
  calculatePaymentProgress,
  formatLayBuyPrice,
} from "@/lib/lay-buy-utils";

interface LayBuyOrder {
  id: string;
  orderNumber: string;
  status: string;
  totalAmount: number;
  amountPaid: number;
  remainingAmount: number;
  dueDate: string;
  gracePeriodEnd: string;
  createdAt: string;
  itemCount: number;
  paymentCount: number;
  refundAmount?: number;
  cancelledAt?: string;
  completedAt?: string;
}

export default function LayBuyProfileSection() {
  const [orders, setOrders] = useState<LayBuyOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<{
    activeOrders: number;
    totalOutstanding: number;
    completedOrders: number;
    cancelledOrders: number;
    totalPaid: number;
  }>({
    activeOrders: 0,
    totalOutstanding: 0,
    completedOrders: 0,
    cancelledOrders: 0,
    totalPaid: 0,
  });

  useEffect(() => {
    fetchLayBuyOrders();
  }, []);

  const fetchLayBuyOrders = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/lay-buy-orders");
      const result = await response.json();

      if (result.success) {
        const orderData = result.data.map((order: any) => ({
          id: order.id,
          orderNumber: order.orderNumber,
          status: order.status,
          totalAmount: order.totalAmount,
          amountPaid: order.amountPaid,
          remainingAmount: order.remainingAmount,
          dueDate: order.dueDate,
          gracePeriodEnd: order.gracePeriodEnd,
          createdAt: order.createdAt,
          itemCount: order.orderItems.length,
          paymentCount: order._count.payments,
          refundAmount: order.refundAmount ?? undefined,
          cancelledAt: order.cancelledAt ?? undefined,
          completedAt: order.completedAt ?? undefined,
        }));

        setOrders(orderData);
        calculateStats(orderData);
      }
    } catch (error) {
      console.error("Error fetching Lay-Buy orders:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateStats = (orderList: LayBuyOrder[]) => {
    const stats = {
      activeOrders: orderList.filter(o => o.status === 'ACTIVE').length,
      totalOutstanding: orderList
        .filter(o => o.status === 'ACTIVE')
        .reduce((sum, o) => sum + (o.totalAmount - o.amountPaid), 0),
      completedOrders: orderList.filter(o => o.status === 'COMPLETED').length,
      cancelledOrders: orderList.filter(o => o.status === 'CANCELLED').length,
      totalPaid: orderList.reduce((sum, o) => sum + o.amountPaid, 0),
    };
    setStats(stats);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: { color: "bg-blue-100 text-blue-800", label: "Active" },
      COMPLETED: { color: "bg-green-100 text-green-800", label: "Completed" },
      CANCELLED: { color: "bg-gray-100 text-gray-800", label: "Cancelled" },
      FORFEITED: { color: "bg-red-100 text-red-800", label: "Forfeited" },
      REFUNDED: { color: "bg-purple-100 text-purple-800", label: "Refunded" },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.ACTIVE;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getUrgencyBadge = (order: LayBuyOrder) => {
    if (order.status !== 'ACTIVE') return null;
    
    const dueDate = new Date(order.dueDate);
    const gracePeriodEnd = new Date(order.gracePeriodEnd);
    const timeRemaining = calculateDaysRemaining(dueDate, gracePeriodEnd);

    if (timeRemaining.status === 'forfeited') {
      return <Badge className="bg-red-100 text-red-800">Expired</Badge>;
    } else if (timeRemaining.isInGracePeriod) {
      return <Badge className="bg-orange-100 text-orange-800">Grace Period</Badge>;
    } else if (timeRemaining.daysRemaining <= 7) {
      return <Badge className="bg-yellow-100 text-yellow-800">Due Soon</Badge>;
    }
    return null;
  };

  const getCountdownDisplay = (order: LayBuyOrder) => {
    if (order.status !== 'ACTIVE') return null;
    
    const dueDate = new Date(order.dueDate);
    const gracePeriodEnd = new Date(order.gracePeriodEnd);
    const timeRemaining = calculateDaysRemaining(dueDate, gracePeriodEnd);

    if (timeRemaining.status === 'forfeited') {
      return (
        <div className="text-red-600 text-sm font-medium">
          Payment period expired
        </div>
      );
    } else if (timeRemaining.isInGracePeriod) {
      return (
        <div className="text-orange-600 text-sm font-medium">
          Grace period: {timeRemaining.daysRemaining} days left
        </div>
      );
    } else {
      return (
        <div className="text-blue-600 text-sm font-medium">
          {timeRemaining.daysRemaining} days remaining
        </div>
      );
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            My Lay-Buy Orders
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            My Lay-Buy Orders
          </CardTitle>
          <Button onClick={fetchLayBuyOrders} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {orders.length === 0 ? (
          <div className="text-center py-8">
            <CreditCard className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Lay-Buy Orders</h3>
            <p className="text-gray-600 mb-4">You haven't placed any Lay-Buy orders yet.</p>
            <Link href="/products">
              <Button>
                <Package className="mr-2 h-4 w-4" />
                Start Shopping
              </Button>
            </Link>
          </div>
        ) : (
          <>
            {/* Stats Summary */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{stats.activeOrders}</div>
                <div className="text-sm text-blue-800">Active Orders</div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{formatLayBuyPrice(stats.totalOutstanding)}</div>
                <div className="text-sm text-orange-800">Outstanding</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{stats.completedOrders}</div>
                <div className="text-sm text-green-800">Completed</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{stats.cancelledOrders}</div>
                <div className="text-sm text-red-800">Cancelled</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{formatLayBuyPrice(stats.totalPaid)}</div>
                <div className="text-sm text-purple-800">Total Paid</div>
              </div>
            </div>

            <Separator />

            {/* Orders List */}
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">Recent Orders</h3>
              {orders.slice(0, 5).map((order) => {
                const paymentProgress = calculatePaymentProgress(order.amountPaid || 0, order.totalAmount);
                const remainingBalance = order.totalAmount - (order.amountPaid || 0);

                return (
                  <div key={order.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold">{order.orderNumber}</h4>
                        <p className="text-sm text-gray-600">
                          {order.itemCount} item{order.itemCount !== 1 ? 's' : ''} • 
                          Created {new Date(order.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(order.status)}
                        {getUrgencyBadge(order)}
                      </div>
                    </div>

                    {order.status === 'ACTIVE' && (
                      <>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Payment Progress</span>
                            <span>{paymentProgress.toFixed(1)}% Complete</span>
                          </div>
                          <Progress value={paymentProgress} className="h-2" />
                        </div>

                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div className="text-center">
                            <div className="font-semibold text-green-600">{formatLayBuyPrice(order.amountPaid || 0)}</div>
                            <div className="text-gray-600">Paid</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold text-orange-600">{formatLayBuyPrice(remainingBalance)}</div>
                            <div className="text-gray-600">Remaining</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold text-blue-600">{formatLayBuyPrice(order.totalAmount || 0)}</div>
                            <div className="text-gray-600">Total</div>
                          </div>
                        </div>

                        {getCountdownDisplay(order)}
                      </>
                    )}

                    {order.status === 'CANCELLED' && (
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="text-center">
                            <div className="font-semibold text-green-600">{formatLayBuyPrice(order.amountPaid || 0)}</div>
                            <div className="text-gray-600">Amount Paid</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold text-red-600">
                              {order.refundAmount ? formatLayBuyPrice(order.refundAmount) : 'TBD'}
                            </div>
                            <div className="text-gray-600">Refund Amount</div>
                          </div>
                        </div>

                        {order.cancelledAt && (
                          <div className="text-sm text-gray-600 text-center">
                            Cancelled on {new Date(order.cancelledAt).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    )}

                    {order.status === 'COMPLETED' && (
                      <div className="text-center text-sm text-green-600">
                        <div className="font-semibold">{formatLayBuyPrice(order.totalAmount || 0)}</div>
                        <div>Fully Paid</div>
                        {order.completedAt && (
                          <div className="text-gray-600 mt-1">
                            Completed on {new Date(order.completedAt).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex justify-between items-center pt-2">
                      <div className="text-sm text-gray-600">
                        {order.paymentCount} payment{order.paymentCount !== 1 ? 's' : ''} made
                      </div>
                      <Link href={`/lay-buy-orders/${order.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </Link>
                    </div>
                  </div>
                );
              })}

              {orders.length > 5 && (
                <div className="text-center pt-4">
                  <Link href="/profile/lay-buy-orders">
                    <Button variant="outline">
                      View All Lay-Buy Orders ({orders.length})
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
