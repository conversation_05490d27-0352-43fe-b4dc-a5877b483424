"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Download,
  FileText,
  Database,
  Calendar,
  Filter,
} from "lucide-react";

export default function LayBuyExport() {
  const [showDialog, setShowDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportConfig, setExportConfig] = useState({
    format: "csv",
    status: "ALL",
    startDate: "",
    endDate: "",
  });

  const handleExport = async () => {
    setIsExporting(true);
    try {
      const params = new URLSearchParams();
      params.append("format", exportConfig.format);
      
      if (exportConfig.status !== "ALL") {
        params.append("status", exportConfig.status);
      }
      
      if (exportConfig.startDate) {
        params.append("startDate", exportConfig.startDate);
      }
      
      if (exportConfig.endDate) {
        params.append("endDate", exportConfig.endDate);
      }

      const response = await fetch(`/api/admin/lay-buy-orders/export?${params}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        
        // Get filename from response headers or generate one
        const contentDisposition = response.headers.get("content-disposition");
        const filename = contentDisposition
          ? contentDisposition.split("filename=")[1]?.replace(/"/g, "")
          : `lay-buy-orders-${new Date().toISOString().split('T')[0]}.${exportConfig.format}`;
        
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        setShowDialog(false);
      } else {
        console.error("Export failed:", response.statusText);
      }
    } catch (error) {
      console.error("Error exporting data:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const resetConfig = () => {
    setExportConfig({
      format: "csv",
      status: "ALL",
      startDate: "",
      endDate: "",
    });
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Export Lay-Buy orders data for reporting, analysis, or record-keeping purposes.
            </p>
            
            <div className="grid grid-cols-2 gap-4">
              <Button
                onClick={() => {
                  setExportConfig(prev => ({ ...prev, format: "csv" }));
                  setShowDialog(true);
                }}
                variant="outline"
                className="h-20 flex-col gap-2"
              >
                <FileText className="h-6 w-6" />
                <span>Export CSV</span>
              </Button>
              
              <Button
                onClick={() => {
                  setExportConfig(prev => ({ ...prev, format: "json" }));
                  setShowDialog(true);
                }}
                variant="outline"
                className="h-20 flex-col gap-2"
              >
                <Database className="h-6 w-6" />
                <span>Export JSON</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Export Configuration Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Export Configuration
            </DialogTitle>
            <DialogDescription>
              Configure your export settings and download the data.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Format Selection */}
            <div>
              <Label htmlFor="format">Export Format</Label>
              <Select 
                value={exportConfig.format} 
                onValueChange={(value) => setExportConfig(prev => ({ ...prev, format: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">CSV (Spreadsheet)</SelectItem>
                  <SelectItem value="json">JSON (Structured Data)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div>
              <Label htmlFor="status">Order Status</Label>
              <Select 
                value={exportConfig.status} 
                onValueChange={(value) => setExportConfig(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Statuses</SelectItem>
                  <SelectItem value="ACTIVE">Active Only</SelectItem>
                  <SelectItem value="COMPLETED">Completed Only</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled Only</SelectItem>
                  <SelectItem value="FORFEITED">Forfeited Only</SelectItem>
                  <SelectItem value="REFUNDED">Refunded Only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={exportConfig.startDate}
                  onChange={(e) => setExportConfig(prev => ({ ...prev, startDate: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={exportConfig.endDate}
                  onChange={(e) => setExportConfig(prev => ({ ...prev, endDate: e.target.value }))}
                />
              </div>
            </div>

            {/* Export Preview */}
            <div className="bg-gray-50 p-3 rounded-lg text-sm">
              <h4 className="font-medium text-gray-900 mb-2">Export Preview</h4>
              <div className="space-y-1 text-gray-600">
                <div>Format: <span className="font-medium">{exportConfig.format.toUpperCase()}</span></div>
                <div>Status: <span className="font-medium">{exportConfig.status}</span></div>
                {exportConfig.startDate && (
                  <div>From: <span className="font-medium">{new Date(exportConfig.startDate).toLocaleDateString()}</span></div>
                )}
                {exportConfig.endDate && (
                  <div>To: <span className="font-medium">{new Date(exportConfig.endDate).toLocaleDateString()}</span></div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              variant="outline" 
              onClick={() => {
                setShowDialog(false);
                resetConfig();
              }}
            >
              Cancel
            </Button>
            <Button 
              variant="outline" 
              onClick={resetConfig}
            >
              <Filter className="h-4 w-4 mr-2" />
              Reset Filters
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting}
            >
              {isExporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
