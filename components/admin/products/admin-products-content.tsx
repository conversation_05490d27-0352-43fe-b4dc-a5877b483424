"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Package,
  DollarSign,
  Star,
  Filter,
  Upload
} from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";
import { Product } from "@/utils/types";
import { getProducts, deleteProduct } from "@/actions/productActions";

export default function AdminProductsContent() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        const result = await getProducts({
          search: searchTerm || undefined
        });

        if (result.success && result.data) {
          // Map result.data to match the Product type
          const mappedProducts = result.data.map((product: any) => ({
            ...product,
            reviews: product.reviews?.map((review: any) => ({
              id: review.id,
              createdAt: review.createdAt ?? new Date(),
              updatedAt: review.updatedAt ?? new Date(),
              userId: review.userId ?? "",
              productId: review.productId ?? "",
              rating: review.rating,
              comment: review.comment ?? null,
              userName: review.userName ?? "",
              userEmail: review.userEmail ?? ""
            })) ?? [],
            _count: product._count
          }));
          setProducts(mappedProducts);
          setFilteredProducts(mappedProducts);
        } else {
          console.error("Error loading products:", result.error);
        }
      } catch (error) {
        console.error("Error loading products:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProducts();
  }, []);

  useEffect(() => {
    const filtered = products.filter(product =>
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.category.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredProducts(filtered);
  }, [searchTerm, products]);

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="destructive">Out of Stock</Badge>;
    } else if (stock < 10) {
      return <Badge variant="secondary">Low Stock ({stock})</Badge>;
    } else {
      return <Badge variant="outline">In Stock ({stock})</Badge>;
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? 
      <Badge variant="default">Active</Badge> : 
      <Badge variant="secondary">Inactive</Badge>;
  };

  const handleDeleteClick = (product: Product) => {
    setProductToDelete(product);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!productToDelete) return;
    
    setIsDeleting(true);
    try {
      const result = await deleteProduct(productToDelete.id);
      if (result.success) {
        // Remove the product from both arrays
        setProducts(prev => prev.filter(p => p.id !== productToDelete.id));
        setFilteredProducts(prev => prev.filter(p => p.id !== productToDelete.id));
        setDeleteDialogOpen(false);
        setProductToDelete(null);
      } else {
        console.error("Error deleting product:", result.error);
        // You might want to show a toast notification here
      }
    } catch (error) {
      console.error("Error deleting product:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleSelectProduct = (id: string) => {
    setSelectedProducts((prev) =>
      prev.includes(id) ? prev.filter((pid) => pid !== id) : [...prev, id]
    );
  };
  const handleSelectAll = () => {
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map((p) => p.id));
    }
  };
  const handleExport = () => {
    // Simple CSV export (placeholder, replace with real export logic)
    const headers = ['ID', 'Name', 'Brand', 'Category', 'Price', 'Stock', 'Active'];
    const rows = filteredProducts.map(p => [p.id, p.name, p.brand, p.category.name, p.price, p.stock, p.isActive ? 'Yes' : 'No']);
    const csv = [headers, ...rows].map(r => r.join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'products.csv';
    a.click();
    URL.revokeObjectURL(url);
  };
  const handleBatchDelete = () => {
    // Placeholder for batch delete logic
    alert(`Batch delete: ${selectedProducts.length} products`);
  };
  const handleBatchUpdate = () => {
    // Placeholder for batch update logic
    alert(`Batch update: ${selectedProducts.length} products`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Products</h1>
          <p className="text-gray-600 mt-2">Manage your product inventory</p>
        </div>
        <div className="flex flex-col xs:flex-row gap-2 mt-2 sm:mt-0">
          <Button variant="outline" onClick={handleExport} title="Export all products as CSV" aria-label="Export Products">
            <Upload className="h-4 w-4 mr-2" />
            Export Products
          </Button>
          <Link href="/products/new">
            <Button className="w-full xs:w-auto">
              <Upload className="h-4 w-4 mr-2" />
              Bulk Upload
            </Button>
          </Link>
          <Link href="/admin/products/new">
            <Button variant="outline" className="w-full xs:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Add Single Product
            </Button>
          </Link>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search products by name, brand, or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Products Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Products</p>
                <p className="text-2xl font-bold">{products.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Active Products</p>
                <p className="text-2xl font-bold">{products.filter(p => p.isActive).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Featured</p>
                <p className="text-2xl font-bold">{products.filter(p => p.isFeatured).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Avg. Price</p>
                <p className="text-2xl font-bold">
                  M{(products.reduce((sum, p) => sum + p.price, 0) / products.length || 0).toFixed(0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Products List */}
      {selectedProducts.length > 0 && (
        <div className="flex gap-2 mb-4 bg-blue-50 border border-blue-200 rounded p-2 items-center">
          <span className="font-medium text-blue-700">{selectedProducts.length} selected</span>
          <Button size="sm" variant="destructive" onClick={handleBatchDelete}>Delete Selected</Button>
          <Button size="sm" variant="outline" onClick={handleBatchUpdate}>Batch Update</Button>
          <Button size="sm" variant="ghost" onClick={() => setSelectedProducts([])}>Clear</Button>
        </div>
      )}
      <Card>
        <CardHeader>
          <CardTitle>Products ({filteredProducts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredProducts.length > 0 && (
              <div className="flex items-center gap-2 mb-2">
                <input
                  type="checkbox"
                  checked={selectedProducts.length === filteredProducts.length}
                  onChange={handleSelectAll}
                  aria-label="Select all products"
                />
                <span className="text-sm text-gray-600">Select All</span>
              </div>
            )}
            {filteredProducts.length === 0 ? (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No products found</p>
                {searchTerm && (
                  <p className="text-sm text-gray-500 mt-2">
                    Try adjusting your search terms
                  </p>
                )}
              </div>
            ) : (
              filteredProducts.map((product) => (
                <div key={product.id} className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-4 flex-1">
                    <input
                      type="checkbox"
                      checked={selectedProducts.includes(product.id)}
                      onChange={() => handleSelectProduct(product.id)}
                      aria-label={`Select product ${product.name}`}
                    />
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                      <Package className="h-6 w-6 text-gray-400" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg break-words max-w-xs md:max-w-none">{product.name}</h3>
                      <p className="text-gray-600 text-sm break-all">{product.brand} • {product.category.name}</p>
                      <div className="flex flex-wrap items-center gap-2 mt-1">
                        {getStatusBadge(product.isActive)}
                        {getStockBadge(product.stock)}
                        {product.isFeatured && <Badge variant="outline">Featured</Badge>}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4 w-full md:w-auto">
                    <div className="text-left md:text-right flex-1">
                      <p className="font-semibold text-lg">
                        M{product.discountedPrice || product.price}
                        {product.discountedPrice && (
                          <span className="text-sm text-gray-500 line-through ml-2">
                            M{product.price}
                          </span>
                        )}
                      </p>
                      <p className="text-sm text-gray-600">
                        {product._count?.reviews || 0} reviews
                      </p>
                    </div>
                    <div className="flex flex-wrap gap-2 justify-start md:justify-end">
                      <Link href={`/admin/products/${product.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/admin/products/${product.id}/edit`}>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        onClick={() => handleDeleteClick(product)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

             {/* Delete Confirmation Dialog */}
       <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
         <DialogContent className="sm:max-w-[425px]">
           <DialogHeader>
             <DialogTitle>Delete Product</DialogTitle>
             <DialogDescription>
               Are you sure you want to delete <strong>"{productToDelete?.name}"</strong>? This action cannot be undone.
               {productToDelete?._count?.orderItems && productToDelete._count.orderItems > 0 && (
                 <span className="block mt-2 text-amber-600">
                   Note: This product has existing orders and will be marked as inactive instead of being deleted.
                 </span>
               )}
             </DialogDescription>
           </DialogHeader>
           <DialogFooter>
             <Button 
               variant="outline" 
               onClick={() => {
                 setDeleteDialogOpen(false);
                 setProductToDelete(null);
               }}
               disabled={isDeleting}
             >
               Cancel
             </Button>
             <Button 
               variant="destructive" 
               onClick={handleDeleteConfirm}
               disabled={isDeleting}
             >
               {isDeleting ? "Deleting..." : "Delete"}
             </Button>
           </DialogFooter>
         </DialogContent>
       </Dialog>
    </div>
  );
}
