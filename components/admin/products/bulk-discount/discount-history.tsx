'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Icons } from '@/components/icons';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

interface DiscountHistoryItem {
  id: string;
  type: 'PERCENTAGE' | 'FIXED_AMOUNT';
  value: number;
  category: { name: string } | null;
  appliedBy: { name: string; email: string };
  productsAffected: number;
  createdAt: string;
}

export function DiscountHistory() {
  const [history, setHistory] = useState<DiscountHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const limit = 5;

  const fetchHistory = async (pageNum: number) => {
    try {
      const response = await fetch(
        `/api/admin/products/bulk-discount/history?page=${pageNum}&limit=${limit}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch discount history');
      }
      
      const result = await response.json();
      
      if (pageNum === 1) {
        setHistory(result.data.data);
      } else {
        setHistory(prev => [...prev, ...result.data.data]);
      }
      
      setHasMore(result.data.data.length === limit);
    } catch (error) {
      console.error('Error fetching discount history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchHistory(1);
  }, []);

  const loadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchHistory(nextPage);
  };

  if (isLoading && history.length === 0) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Discount History</CardTitle>
        <CardDescription>Recent bulk discount applications</CardDescription>
      </CardHeader>
      <CardContent>
        {history.length === 0 ? (
          <div className="text-center py-8">
            <Icons.history className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No discount history</h3>
            <p className="mt-1 text-sm text-gray-500">
              Apply your first bulk discount to see history here.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead>Applied By</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {history.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="whitespace-nowrap">
                      {format(new Date(item.createdAt), 'MMM d, yyyy')}
                      <div className="text-xs text-muted-foreground">
                        {format(new Date(item.createdAt), 'h:mm a')}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={item.type === 'PERCENTAGE' ? 'default' : 'secondary'}>
                        {item.type === 'PERCENTAGE' ? 'Percentage' : 'Fixed'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {item.type === 'PERCENTAGE' 
                        ? `${item.value}%` 
                        : `M${item.value.toFixed(2)}`}
                    </TableCell>
                    <TableCell>
                      {item.category?.name || 'All Categories'}
                    </TableCell>
                    <TableCell>{item.productsAffected}</TableCell>
                    <TableCell className="whitespace-nowrap">
                      <div className="font-medium">{item.appliedBy.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {item.appliedBy.email}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {hasMore && (
              <div className="mt-4 flex justify-center">
                <Button 
                  variant="outline" 
                  onClick={loadMore}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  Load More
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
