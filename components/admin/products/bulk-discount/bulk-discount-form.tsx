'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useCategories } from '@/hooks/use-categories';
import { Icons } from '@/components/icons';

const discountFormSchema = z.object({
  discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT'], {
    required_error: 'Please select a discount type',
  }),
  value: z.coerce.number().positive('Value must be greater than 0'),
  categoryId: z.string().optional(),
  applyToAll: z.boolean(),
});

type DiscountFormValues = z.infer<typeof discountFormSchema>;

export function BulkDiscountForm() {
  const [isLoading, setIsLoading] = useState(false);
  const { data: categories = [] } = useCategories();
  const router = useRouter();

  const form = useForm<DiscountFormValues>({
    resolver: zodResolver(discountFormSchema),
    defaultValues: {
      discountType: 'PERCENTAGE',
      value: 10,
      applyToAll: false,
    },
  });

  const applyToAll = form.watch('applyToAll');
  const discountType = form.watch('discountType');

  async function onSubmit(data: DiscountFormValues) {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/admin/products/bulk-discount', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          discountType: data.discountType,
          value: Number(data.value),
          categoryId: data.applyToAll ? undefined : data.categoryId,
          applyToAll: data.applyToAll,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to apply discount');
      }

      toast.success(`Successfully applied discount to ${result.data.productsAffected} products`);
      router.refresh();
    } catch (error) {
      console.error('Error applying discount:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to apply discount');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Apply Bulk Discount</CardTitle>
        <CardDescription>
          Apply a discount to multiple products at once. You can apply to all products or filter by category.
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="discountType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Discount Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a discount type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                        <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {discountType === 'PERCENTAGE' 
                        ? 'The discount will be calculated as a percentage of the product price.'
                        : 'The discount will be a fixed amount subtracted from the product price.'}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {discountType === 'PERCENTAGE' ? 'Discount Percentage' : 'Discount Amount (M)'}
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <span className="text-gray-500 sm:text-sm">
                            {discountType === 'PERCENTAGE' ? '%' : 'M'}
                          </span>
                        </div>
                        <Input
                          type="number"
                          step={discountType === 'PERCENTAGE' ? '1' : '0.01'}
                          min="0.01"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <FormField
                control={form.control}
                name="applyToAll"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Apply to all products</FormLabel>
                      <FormDescription>
                        When enabled, the discount will be applied to all active products.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {!applyToAll && (
                <FormField
                  control={form.control}
                  name="categoryId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <div className="rounded-md bg-blue-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <Icons.alertCircle className="h-5 w-5 text-blue-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">Important Notice</h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <p>
                      This action will update the <strong>discounted price</strong> of all selected products.
                      Existing discounts will be overwritten.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end border-t px-6 py-4">
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
              Apply Discount
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
