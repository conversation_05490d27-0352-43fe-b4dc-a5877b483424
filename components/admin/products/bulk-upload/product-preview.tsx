"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Upload, 
  Eye, 
  Package, 
  CheckCircle,
  AlertCircle,
  Edit,
  Image as ImageIcon
} from "lucide-react";
import { ProductData, UploadedImage } from "./bulk-upload-content";

interface ProductPreviewProps {
  products: ProductData[];
  uploadedImages: UploadedImage[];
  onSubmit: () => void;
  onBack: () => void;
  isProcessing: boolean;
}

export default function ProductPreview({
  products,
  uploadedImages,
  onSubmit,
  onBack,
  isProcessing
}: ProductPreviewProps) {
  const [expandedProduct, setExpandedProduct] = useState<string | null>(null);
  const [reviewChecklist, setReviewChecklist] = useState({
    productNames: false,
    pricing: false,
    categories: false,
    images: false,
    inventory: false,
  });
  const [showValidationDetails, setShowValidationDetails] = useState(false);
  const allChecked = Object.values(reviewChecklist).every(Boolean);
  const someChecked = Object.values(reviewChecklist).some(Boolean);

  const getImageByUrl = (url: string) => {
    return uploadedImages.find(img => img.url === url);
  };

  const formatPrice = (price: number) => {
    return `M ${price.toFixed(2)}`;
  };

  const getProductSummary = () => {
    const totalProducts = products.length;
    const totalImages = products.reduce((sum, product) => sum + product.images.length, 0);
    const productsWithImages = products.filter(product => product.images.length > 0).length;
    const productsWithoutImages = totalProducts - productsWithImages;

    return {
      totalProducts,
      totalImages,
      productsWithImages,
      productsWithoutImages,
    };
  };

  const summary = getProductSummary();

  const toggleProductExpansion = (productId: string) => {
    setExpandedProduct(expandedProduct === productId ? null : productId);
  };

  const getValidationIssues = () => {
    const issues: string[] = [];

    products.forEach((product, index) => {
      if (!product.name.trim()) {
        issues.push(`Product ${index + 1}: Missing name`);
      }
      if (!product.price || product.price <= 0) {
        issues.push(`Product ${index + 1}: Invalid price`);
      }
      if (!product.brand.trim()) {
        issues.push(`Product ${index + 1}: Missing brand`);
      }
      if (!product.categoryId) {
        issues.push(`Product ${index + 1}: Missing category`);
      }
      if (product.images.length === 0) {
        issues.push(`Product ${index + 1}: No images assigned`);
      }
    });

    return issues;
  };

  const validationIssues = getValidationIssues();
  const canSubmit = validationIssues.length === 0 && Object.values(reviewChecklist).every(Boolean);

  const handleSelectAll = (checked: boolean) => {
    setReviewChecklist({
      productNames: checked,
      pricing: checked,
      categories: checked,
      images: checked,
      inventory: checked,
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <Eye className="w-12 h-12 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Review Products</h2>
        <p className="text-gray-600">
          Review all products before submitting. Make sure all information is correct.
        </p>
      </div>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="w-5 h-5" />
            <span>Upload Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{summary.totalProducts}</div>
              <div className="text-sm text-gray-600">Total Products</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{summary.totalImages}</div>
              <div className="text-sm text-gray-600">Total Images</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{summary.productsWithImages}</div>
              <div className="text-sm text-gray-600">With Images</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{summary.productsWithoutImages}</div>
              <div className="text-sm text-gray-600">Without Images</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Review Checklist */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Pre-Submission Review</span>
            {validationIssues.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowValidationDetails(!showValidationDetails)}
              >
                <AlertCircle className="w-4 h-4 mr-1" />
                {validationIssues.length} Issues
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {/* Select All Checkbox */}
            <div className="flex items-center space-x-2 mb-2">
              <input
                type="checkbox"
                id="selectAll"
                checked={allChecked}
                ref={el => { if (el) el.indeterminate = !allChecked && someChecked; }}
                onChange={e => handleSelectAll(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="selectAll" className="font-medium text-sm">
                Select All
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="productNames"
                checked={reviewChecklist.productNames}
                onChange={(e) => setReviewChecklist(prev => ({ ...prev, productNames: e.target.checked }))}
                className="rounded"
              />
              <label htmlFor="productNames" className="text-sm">
                All product names are accurate and descriptive
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="pricing"
                checked={reviewChecklist.pricing}
                onChange={(e) => setReviewChecklist(prev => ({ ...prev, pricing: e.target.checked }))}
                className="rounded"
              />
              <label htmlFor="pricing" className="text-sm">
                Pricing information is correct (including discounts)
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="categories"
                checked={reviewChecklist.categories}
                onChange={(e) => setReviewChecklist(prev => ({ ...prev, categories: e.target.checked }))}
                className="rounded"
              />
              <label htmlFor="categories" className="text-sm">
                Products are assigned to correct categories
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="images"
                checked={reviewChecklist.images}
                onChange={(e) => setReviewChecklist(prev => ({ ...prev, images: e.target.checked }))}
                className="rounded"
              />
              <label htmlFor="images" className="text-sm">
                Product images are properly assigned and high quality
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="inventory"
                checked={reviewChecklist.inventory}
                onChange={(e) => setReviewChecklist(prev => ({ ...prev, inventory: e.target.checked }))}
                className="rounded"
              />
              <label htmlFor="inventory" className="text-sm">
                Stock quantities and variants (sizes) are accurate
              </label>
            </div>
          </div>

          {showValidationDetails && validationIssues.length > 0 && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <h4 className="font-medium text-red-800 mb-2">Validation Issues:</h4>
              <ul className="text-sm text-red-700 space-y-1">
                {validationIssues.map((issue, index) => (
                  <li key={index}>• {issue}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Warnings */}
      {summary.productsWithoutImages > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {summary.productsWithoutImages} product(s) don't have images assigned.
            Products without images will still be created but may not display properly in the store.
          </AlertDescription>
        </Alert>
      )}

      {validationIssues.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please fix {validationIssues.length} validation issue(s) before submitting.
          </AlertDescription>
        </Alert>
      )}

      {/* Products List */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Products to be Created</h3>
        
        {products.map((product, index) => (
          <Card key={product.id} className="overflow-hidden">
            <CardContent className="p-0">
              <div 
                className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => toggleProductExpansion(product.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                      {product.images.length > 0 ? (
                        <img
                          src={product.images[0]}
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <ImageIcon className="w-6 h-6 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg text-gray-900">{product.name}</h4>
                      <p className="text-gray-600">{product.brand}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline">{formatPrice(product.price)}</Badge>
                        {product.discountedPrice && (
                          <Badge variant="secondary">{formatPrice(product.discountedPrice)}</Badge>
                        )}
                        <Badge variant="outline">{product.images.length} images</Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {product.images.length === 0 && (
                      <Badge variant="destructive">No Images</Badge>
                    )}
                    <Button variant="ghost" size="sm">
                      {expandedProduct === product.id ? 'Collapse' : 'Expand'}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Expanded Details */}
              {expandedProduct === product.id && (
                <div className="border-t bg-gray-50 p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Product Details</h5>
                      <div className="space-y-2 text-sm">
                        <div><span className="font-medium">Description:</span> {product.description || 'No description'}</div>
                        <div><span className="font-medium">Stock:</span> {product.stock}</div>
                        <div><span className="font-medium">Status:</span> {product.isActive ? 'Active' : 'Inactive'}</div>
                        {product.sizes.length > 0 && (
                          <div>
                            <span className="font-medium">Sizes:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {product.sizes.map(size => (
                                <Badge key={size} variant="outline" className="text-xs">{size}</Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Product Images</h5>
                      {product.images.length > 0 ? (
                        <div className="grid grid-cols-4 gap-2">
                          {product.images.map((imageUrl, imgIndex) => {
                            const image = getImageByUrl(imageUrl);
                            return (
                              <div key={imgIndex} className="aspect-square bg-gray-100 rounded overflow-hidden">
                                <img
                                  src={imageUrl}
                                  alt={`${product.name} ${imgIndex + 1}`}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <ImageIcon className="w-8 h-8 mx-auto mb-2" />
                          <p>No images assigned</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={onBack} disabled={isProcessing}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Edit
        </Button>
        <Button
          onClick={onSubmit}
          disabled={isProcessing || !canSubmit}
          className="px-8"
        >
          {isProcessing ? (
            <>
              <Upload className="w-4 h-4 mr-2 animate-spin" />
              Creating Products...
            </>
          ) : (
            <>
              <Upload className="w-4 h-4 mr-2" />
              Create {products.length} Products
              {!canSubmit && (
                <span className="ml-2 text-xs opacity-75">
                  (Complete review first)
                </span>
              )}
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
