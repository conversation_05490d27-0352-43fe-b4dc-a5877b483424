"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  BarChart3,
  RefreshCw,
  Eye,
  DollarSign,
  ShoppingCart
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface ValidationLog {
  id: string;
  orderId: string;
  originalTotal: number;
  correctedTotal: number;
  discrepancy: number;
  correctionType: string;
  details: any;
  createdAt: string;
}

interface ValidationStats {
  totalLogs: number;
  averageDiscrepancy: number;
  maxDiscrepancy: number;
  commonCorrectionTypes: Array<{
    type: string;
    count: number;
  }>;
}

export default function ValidationDashboard() {
  const [logs, setLogs] = useState<ValidationLog[]>([]);
  const [stats, setStats] = useState<ValidationStats>({
    totalLogs: 0,
    averageDiscrepancy: 0,
    maxDiscrepancy: 0,
    commonCorrectionTypes: []
  });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    fetchValidationData();
  }, []);

  const fetchValidationData = async () => {
    try {
      const response = await fetch('/api/admin/order-validation/logs');
      if (response.ok) {
        const data = await response.json();
        setLogs(data.logs);
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Error fetching validation data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCorrectionTypeBadge = (type: string) => {
    switch (type) {
      case 'discount_error':
        return <Badge variant="destructive">Discount Error</Badge>;
      case 'delivery_fee':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Delivery Fee</Badge>;
      case 'calculation':
        return <Badge variant="outline">Calculation</Badge>;
      case 'lay_buy_error':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">Lay-Buy Error</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => `M${amount.toFixed(2)}`;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <RefreshCw className="w-6 h-6 animate-spin" />
        <span className="ml-2">Loading validation data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Order Validation Dashboard</h1>
          <p className="text-gray-600 mt-2">Monitor order calculation accuracy and system corrections</p>
        </div>
        <Button onClick={fetchValidationData} className="flex items-center gap-2">
          <RefreshCw className="w-4 h-4" />
          Refresh Data
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Validations</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalLogs}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Discrepancy</p>
                <p className="text-3xl font-bold text-orange-600">{formatCurrency(stats.averageDiscrepancy)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Max Discrepancy</p>
                <p className="text-3xl font-bold text-red-600">{formatCurrency(stats.maxDiscrepancy)}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">System Health</p>
                <p className="text-lg font-bold text-green-600">
                  {stats.averageDiscrepancy < 5 ? 'Good' : stats.averageDiscrepancy < 15 ? 'Fair' : 'Needs Attention'}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alert for high discrepancies */}
      {stats.maxDiscrepancy > 50 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            High discrepancies detected (max: {formatCurrency(stats.maxDiscrepancy)}). 
            Please review recent orders for potential system issues.
          </AlertDescription>
        </Alert>
      )}

      {/* Tabs for detailed views */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="logs">Validation Logs</TabsTrigger>
          <TabsTrigger value="analysis">Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Common Correction Types</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stats.commonCorrectionTypes.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getCorrectionTypeBadge(item.type)}
                      </div>
                      <span className="font-medium">{item.count} occurrences</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {logs.slice(0, 5).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Order {log.orderId}</div>
                        <div className="text-sm text-gray-500">
                          {new Date(log.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-red-600">
                          {formatCurrency(log.discrepancy)}
                        </div>
                        {getCorrectionTypeBadge(log.correctionType)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Validation Logs</CardTitle>
            </CardHeader>
            <CardContent>
              {logs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No validation logs found
                </div>
              ) : (
                <div className="space-y-4">
                  {logs.map((log) => (
                    <div key={log.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium">Order {log.orderId}</div>
                        <div className="flex items-center gap-2">
                          {getCorrectionTypeBadge(log.correctionType)}
                          <span className="text-sm text-gray-500">
                            {new Date(log.createdAt).toLocaleString()}
                          </span>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Original:</span>
                          <div className="font-medium">{formatCurrency(log.originalTotal)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Corrected:</span>
                          <div className="font-medium">{formatCurrency(log.correctedTotal)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Discrepancy:</span>
                          <div className="font-medium text-red-600">{formatCurrency(log.discrepancy)}</div>
                        </div>
                      </div>
                      
                      {log.details && (
                        <div className="mt-3 p-2 bg-gray-50 rounded text-xs">
                          <pre>{JSON.stringify(log.details, null, 2)}</pre>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  Analysis of order validation patterns and recommendations for system improvements.
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Accuracy Rate</h4>
                    <div className="text-2xl font-bold text-green-600">
                      {stats.totalLogs > 0 ? 
                        ((1 - (stats.totalLogs / 1000)) * 100).toFixed(1) : 100}%
                    </div>
                    <p className="text-sm text-gray-600">Orders processed without issues</p>
                  </div>
                  
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Total Corrections</h4>
                    <div className="text-2xl font-bold text-blue-600">{stats.totalLogs}</div>
                    <p className="text-sm text-gray-600">Automatic corrections applied</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
