"use client";

import { useState, useEffect } from "react";
import { CheckoutData } from "./checkout-content";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowRight, MessageCircle, AlertCircle, CreditCard, Clock, MapPin } from "lucide-react";
import {
  validateLesothoPhoneNumber,
  validateCountryRestriction,
  LESOTHO_DISTRICTS,
} from "@/lib/product-utils";
import { Checkbox } from "@/components/ui/checkbox";
import { useCart } from "@/contexts/cart-context";
import { calculateLayBuyAmounts, isEligibleForLayBuy, formatLayBuyPrice } from "@/lib/lay-buy-utils";
import { calculateDelivery<PERSON>ee, getFinalOrderTotal } from "@/lib/product-utils";
import LayBuyTermsModal from "@/components/lay-buy/lay-buy-terms-modal";

interface CheckoutStepShippingProps {
  data: CheckoutData;
  onUpdate: (data: Partial<CheckoutData>) => void;
  onNext: () => void;
}

export default function CheckoutStepShipping({
  data,
  onUpdate,
  onNext,
}: CheckoutStepShippingProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [wantsLayBuy, setWantsLayBuy] = useState(data.layBuy?.enabled || false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [flashMessage, setFlashMessage] = useState<string | null>(null);
  const { state: cartState } = useCart();

  useEffect(() => {
    if (wantsLayBuy && !data.layBuy?.termsAccepted) {
      setShowTermsModal(true);
    }
  }, [wantsLayBuy, data.layBuy?.termsAccepted]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!data.shipping.fullName.trim()) {
      newErrors.fullName = "Full name is required";
    }

    if (!data.shipping.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(data.shipping.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!data.shipping.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else {
      const phoneValidation = validateLesothoPhoneNumber(data.shipping.phone);
      if (!phoneValidation.isValid) {
        newErrors.phone = phoneValidation.error || "Invalid phone number";
      }
    }

    if (!data.shipping.address.trim()) {
      newErrors.address = "Address is required";
    }

    if (!data.shipping.city.trim()) {
      newErrors.city = "City is required";
    }

    if (!data.shipping.district.trim()) {
      newErrors.district = "District is required";
    }

    if (!data.shipping.postalCode.trim()) {
      newErrors.postalCode = "Postal code is required";
    }

    if (!data.shipping.country.trim()) {
      newErrors.country = "Country is required";
    } else {
      const countryValidation = validateCountryRestriction(data.shipping.country);
      if (!countryValidation.isValid) {
        newErrors.country = countryValidation.error || "Invalid country";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (wantsLayBuy && !data.layBuy?.termsAccepted) {
      setShowTermsModal(true);
      setFlashMessage("You must read and accept the Lay-Buy Terms & Conditions before proceeding.");
      return;
    }
    if (validateForm()) {
      setFlashMessage(null);
      onNext();
    }
  };

  const updateShipping = (field: string, value: string) => {
    // Auto-format phone number for Lesotho
    if (field === "phone") {
      const phoneValidation = validateLesothoPhoneNumber(value);
      if (phoneValidation.isValid && phoneValidation.formatted) {
        value = phoneValidation.formatted;
      }
    }

    const updatedShipping = {
      ...data.shipping,
      [field]: value,
    };

    onUpdate({
      shipping: updatedShipping,
    });

    // If district changed and laybuy is enabled, recalculate amounts
    if (field === "district" && data.layBuy?.enabled) {
      const newDeliveryInfo = calculateDeliveryFee(value, subtotal);
      const newDeliveryCost = newDeliveryInfo.fee;
      const newTotalAmount = getFinalOrderTotal(subtotal, 0, newDeliveryCost);
      const newLayBuyAmounts = calculateLayBuyAmounts(newTotalAmount);

      onUpdate({
        layBuy: {
          ...data.layBuy,
          upfrontAmount: newLayBuyAmounts.upfrontAmount,
          remainingAmount: newLayBuyAmounts.remainingAmount,
          totalAmount: newTotalAmount,
          deliveryFee: newDeliveryCost,
        }
      });
    }

    // Clear error for this field
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  // Calculate total amount for Lay-Buy eligibility
  const subtotal = cartState.totalPrice - data.discountAmount;
  
  // Calculate delivery fee based on district
  const deliveryInfo = calculateDeliveryFee(data.shipping.district, subtotal);
  const deliveryCost = deliveryInfo.fee;
  const totalAmount = getFinalOrderTotal(subtotal, 0, deliveryCost);
  
  const isLayBuyEligible = isEligibleForLayBuy(totalAmount);
  const layBuyAmounts = calculateLayBuyAmounts(totalAmount);

  const handleLayBuyChange = (checked: boolean) => {
    setWantsLayBuy(checked);
    
    if (checked) {
      onUpdate({
        layBuy: {
          enabled: true,
          termsAccepted: false,
          upfrontAmount: layBuyAmounts.upfrontAmount,
          remainingAmount: layBuyAmounts.remainingAmount,
          totalAmount: totalAmount,
          deliveryFee: deliveryCost,
        }
      });
    } else {
      onUpdate({
        layBuy: {
          enabled: false,
          termsAccepted: false,
          upfrontAmount: 0,
          remainingAmount: 0,
          totalAmount: 0,
          deliveryFee: 0,
        }
      });
    }
  };

  const handleAcceptTerms = () => {
    onUpdate({
      layBuy: {
        ...data.layBuy!,
        termsAccepted: true,
      }
    });
    setShowTermsModal(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {flashMessage && (
        <Alert className="mb-4 border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">{flashMessage}</AlertDescription>
        </Alert>
      )}
      {/* Lay-Buy Option */}
      <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center gap-2 mb-2">
          <CreditCard className="h-5 w-5 text-blue-600" />
          <Label className="font-semibold text-blue-800">Lay-Buy Payment Option</Label>
        </div>

        {isLayBuyEligible ? (
          <>
            <div className="flex items-center gap-2 mb-3">
              <Checkbox
                id="laybuy"
                checked={wantsLayBuy}
                onCheckedChange={handleLayBuyChange}
                disabled={!isLayBuyEligible}
              />
              <Label htmlFor="laybuy" className="font-medium text-blue-800">
                Pay only {formatLayBuyPrice(layBuyAmounts.upfrontAmount)} now, {formatLayBuyPrice(layBuyAmounts.remainingAmount)} later
              </Label>
            </div>

            {wantsLayBuy && (
              <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center gap-2 text-green-800 mb-2">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">Lay-Buy Activated</span>
                </div>
                <div className="text-sm text-green-700">
                  <p>• Order Subtotal: {formatLayBuyPrice(subtotal)}</p>
                  <p>• Delivery Fee ({data.shipping.district || 'Select District'}): {formatLayBuyPrice(deliveryCost)}</p>
                  <p>• Total Amount: {formatLayBuyPrice(totalAmount)}</p>
                  <p>• Upfront Payment: {formatLayBuyPrice(layBuyAmounts.upfrontAmount)}</p>
                  <p>• Remaining Balance: {formatLayBuyPrice(layBuyAmounts.remainingAmount)}</p>
                  <p>• Payment Period: 6 weeks</p>
                  <p>• Grace Period: 1 week</p>
                </div>
              </div>
            )}
          </>
        ) : (
          <p className="text-sm text-gray-600">
            Lay-Buy is available for orders over M500. Your order total is {formatLayBuyPrice(totalAmount)}.
          </p>
        )}
      </div>

      {/* Contact Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="fullName">Full Name *</Label>
            <Input
              id="fullName"
              value={data.shipping.fullName}
              onChange={(e) => updateShipping("fullName", e.target.value)}
              placeholder="Enter your full name"
              className={errors.fullName ? "border-red-500" : ""}
            />
            {errors.fullName && (
              <p className="text-sm text-red-500 mt-1">{errors.fullName}</p>
            )}
          </div>

          <div>
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              value={data.shipping.email}
              onChange={(e) => updateShipping("email", e.target.value)}
              placeholder="Enter your email address"
              className={errors.email ? "border-red-500" : ""}
            />
            {errors.email && (
              <p className="text-sm text-red-500 mt-1">{errors.email}</p>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="phone">Phone Number *</Label>
          <Input
            id="phone"
            value={data.shipping.phone}
            onChange={(e) => updateShipping("phone", e.target.value)}
            placeholder="Enter your phone number (e.g., +266 5316 3354)"
            className={errors.phone ? "border-red-500" : ""}
          />
          {errors.phone && (
            <p className="text-sm text-red-500 mt-1">{errors.phone}</p>
          )}
        </div>
      </div>

      {/* Address Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Address Information</h3>
        
        <div>
          <Label htmlFor="address">Address *</Label>
          <Input
            id="address"
            value={data.shipping.address}
            onChange={(e) => updateShipping("address", e.target.value)}
            placeholder="Enter your street address"
            className={errors.address ? "border-red-500" : ""}
          />
          {errors.address && (
            <p className="text-sm text-red-500 mt-1">{errors.address}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="city">City *</Label>
            <Input
              id="city"
              value={data.shipping.city}
              onChange={(e) => updateShipping("city", e.target.value)}
              placeholder="Enter your city"
              className={errors.city ? "border-red-500" : ""}
            />
            {errors.city && (
              <p className="text-sm text-red-500 mt-1">{errors.city}</p>
            )}
          </div>

          <div>
            <Label htmlFor="district">District *</Label>
            <Select
              value={data.shipping.district}
              onValueChange={(value) => updateShipping("district", value)}
            >
              <SelectTrigger className={errors.district ? "border-red-500" : ""}>
                <SelectValue placeholder="Select your district" />
              </SelectTrigger>
              <SelectContent>
                {LESOTHO_DISTRICTS.map((district) => (
                  <SelectItem key={district} value={district}>
                    {district}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.district && (
              <p className="text-sm text-red-500 mt-1">{errors.district}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="postalCode">Postal Code *</Label>
            <Input
              id="postalCode"
              value={data.shipping.postalCode}
              onChange={(e) => updateShipping("postalCode", e.target.value)}
              placeholder="Enter your postal code"
              className={errors.postalCode ? "border-red-500" : ""}
            />
            {errors.postalCode && (
              <p className="text-sm text-red-500 mt-1">{errors.postalCode}</p>
            )}
          </div>

          <div>
            <Label htmlFor="country">Country *</Label>
            <Select
              value={data.shipping.country}
              onValueChange={(value) => updateShipping("country", value)}
            >
              <SelectTrigger className={errors.country ? "border-red-500" : ""}>
                <SelectValue placeholder="Select your country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Lesotho">Lesotho</SelectItem>
                <SelectItem value="South Africa">South Africa</SelectItem>
              </SelectContent>
            </Select>
            {errors.country && (
              <p className="text-sm text-red-500 mt-1">{errors.country}</p>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="notes">Special Instructions (Optional)</Label>
          <Textarea
            id="notes"
            value={data.shipping.notes || ""}
            onChange={(e) => updateShipping("notes", e.target.value)}
            placeholder="Any special delivery instructions, landmarks, or notes for the delivery driver"
            rows={3}
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" className="w-full md:w-auto">
          Continue to Review
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>

      {/* Lay-Buy Terms Modal */}
      <LayBuyTermsModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
        onAccept={handleAcceptTerms}
        totalAmount={totalAmount}
        upfrontAmount={layBuyAmounts.upfrontAmount}
      />
    </form>
  );
}
