"use client";

import { useState, useEffect } from "react";
import { Order, OrderStatus } from "@/utils/types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Package,
  Calendar,
  CreditCard,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  MapPin,
  Phone,
  FileText
} from "lucide-react";
import Link from "next/link";
import { formatPrice, calculateDeliveryFee } from "@/lib/product-utils";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

interface OrderDetailContentProps {
  orderId: string;
}

export default function OrderDetailContent({ orderId }: OrderDetailContentProps) {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/orders/${orderId}`);
        const result = await response.json();

        if (result.success) {
          setOrder(result.data);
        } else {
          setError(result.error || "Order not found");
        }
      } catch (err) {
        setError("Failed to fetch order details");
        console.error("Error fetching order:", err);
      } finally {
        setLoading(false);
      }
    };

    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case "PENDING":
        return <Clock className="h-5 w-5" />;
      case "CONFIRMED":
        return <CheckCircle className="h-5 w-5" />;
      case "PROCESSING":
        return <Package className="h-5 w-5" />;
      case "SHIPPED":
        return <Truck className="h-5 w-5" />;
      case "DELIVERED":
        return <CheckCircle className="h-5 w-5" />;
      case "CANCELLED":
        return <XCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "CONFIRMED":
        return "bg-blue-100 text-blue-800";
      case "PROCESSING":
        return "bg-purple-100 text-purple-800";
      case "SHIPPED":
        return "bg-orange-100 text-orange-800";
      case "DELIVERED":
        return "bg-green-100 text-green-800";
      case "CANCELLED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getOrderProgress = (status: OrderStatus) => {
    const steps = ["PENDING", "CONFIRMED", "PROCESSING", "SHIPPED", "DELIVERED"];
    const currentIndex = steps.indexOf(status);
    return currentIndex >= 0 ? ((currentIndex + 1) / steps.length) * 100 : 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="max-w-2xl mx-auto text-center py-12">
        <Package className="mx-auto h-16 w-16 text-gray-400 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Order not found</h2>
        <p className="text-gray-600 mb-6">{error}</p>
        <Link href="/orders">
          <Button>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Button>
        </Link>
      </div>
    );
  }

  // Extract district from shipping address first
  // Address format: "address, city, district, postalCode, country"
  const addressParts = order.shippingAddress.split(', ');
  let district = 'Maseru'; // Default fallback

  if (addressParts.length >= 3) {
    // District should be the third part (index 2)
    district = addressParts[2].trim();
  } else if (addressParts.length === 2) {
    // If only 2 parts, assume second part is district
    district = addressParts[1].trim();
  } else if (addressParts.length === 1) {
    // If only one part, check if it contains a known district name
    const fullAddress = addressParts[0].toLowerCase();
    const knownDistricts = ['maseru', 'berea', 'leribe', 'mohale\'s hoek', 'mafeteng', 'qacha\'s nek', 'thaba-tseka', 'mokhotlong', 'quthing', 'butha-buthe'];
    const foundDistrict = knownDistricts.find(d => fullAddress.includes(d));
    if (foundDistrict) {
      district = foundDistrict.charAt(0).toUpperCase() + foundDistrict.slice(1);
    }
  }

  // Calculate subtotal as sum of all item prices
  const subtotal = order.orderItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const discount = order.discountAmount || 0;

  // Recalculate delivery fee based on district to ensure accuracy
  // (This handles cases where old orders had incorrect delivery fees)
  const subtotalAfterDiscount = subtotal - discount;
  const deliveryInfo = calculateDeliveryFee(district, subtotalAfterDiscount);
  const correctDeliveryFee = deliveryInfo.fee;

  // Use the recalculated delivery fee for display
  const deliveryCost = correctDeliveryFee;
  const total = subtotal - discount + deliveryCost;

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <Link href="/orders">
            <Button variant="outline" size="sm" className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Orders
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">
            Order #{order.orderNumber}
          </h1>
          <p className="text-gray-600">
            Placed on {new Date(order.createdAt).toLocaleDateString()}
          </p>
        </div>
        
        <Badge className={`${getStatusColor(order.status)} text-lg px-4 py-2`}>
          <div className="flex items-center gap-2">
            {getStatusIcon(order.status)}
            {order.status}
          </div>
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Progress */}
          {order.status !== "CANCELLED" && (
            <Card>
              <CardHeader>
                <CardTitle>Order Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${getOrderProgress(order.status)}%` }}
                    ></div>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    {["PENDING", "CONFIRMED", "PROCESSING", "SHIPPED", "DELIVERED"].map((status, index) => {
                      const isActive = ["PENDING", "CONFIRMED", "PROCESSING", "SHIPPED", "DELIVERED"].indexOf(order.status) >= index;
                      return (
                        <div 
                          key={status}
                          className={`text-center ${isActive ? "text-blue-600" : "text-gray-400"}`}
                        >
                          <div className={`w-3 h-3 rounded-full mx-auto mb-1 ${isActive ? "bg-blue-600" : "bg-gray-300"}`}></div>
                          <span className="text-xs">{status}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle>Order Items ({order.orderItems.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.orderItems.map((item) => (
                  <div key={item.id} className="flex gap-4 py-4 border-b last:border-b-0">
                    <div className="flex-shrink-0">
                      <img
                        src={item.product.images[0] || "/placeholder.png"}
                        alt={item.product.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.product.name}</h4>
                      <p className="text-sm text-gray-500">{item.product.brand}</p>
                      
                      <div className="flex items-center gap-2 mt-1">
                        {item.size && (
                          <Badge variant="outline" className="text-xs">
                            Size: {item.size}
                          </Badge>
                        )}
                        {item.color && (
                          <Badge variant="outline" className="text-xs">
                            Color: {item.color}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-sm text-gray-600">
                          Quantity: {item.quantity}
                        </span>
                        <div className="text-right">
                          <div className="font-medium">
                            {formatPrice(item.price * item.quantity)}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatPrice(item.price)} each
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment Information */}
          {order.paymentProof && (
            <Card>
              <CardHeader>
                <CardTitle>Payment Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Payment Status:</span>
                    <Badge 
                      variant={order.paymentProof.status === "VERIFIED" ? "default" : "secondary"}
                    >
                      {order.paymentProof.status}
                    </Badge>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Payment Proof</h4>
                    <img
                      src={order.paymentProof.imageUrl}
                      alt="Payment proof"
                      className="max-w-full h-auto max-h-64 rounded-lg border"
                    />
                  </div>
                  
                  {order.paymentProof.notes && (
                    <div>
                      <h4 className="font-medium mb-2">Admin Notes</h4>
                      <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                        {order.paymentProof.notes}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Subtotal ({order.orderItems.length} items)</span>
                  <span>{formatPrice(subtotal)}</span>
                </div>
                {discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount {order.discountCode && `(${order.discountCode.code})`}</span>
                    <span>-{formatPrice(discount)}</span>
                  </div>
                )}
                <div className="flex justify-between text-sm"><span>Delivery District</span><span>{district}</span></div>
                <div className="flex justify-between text-sm">
                  <span>Delivery</span>
                  <span className={deliveryCost === 0 ? "text-green-600" : ""}>
                    {deliveryCost === 0 ? "Free" : formatPrice(deliveryCost)}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>{formatPrice(total)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Information */}
          <Card>
            <CardHeader>
              <CardTitle>Shipping Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-2">
                <MapPin className="h-4 w-4 mt-1 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Delivery Address</p>
                  <p className="text-sm text-gray-600">{order.shippingAddress}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Phone Number</p>
                  <p className="text-sm text-gray-600">{order.phoneNumber}</p>
                </div>
              </div>
              
              {order.notes && (
                <div className="flex items-start gap-2">
                  <FileText className="h-4 w-4 mt-1 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Delivery Notes</p>
                    <p className="text-sm text-gray-600">{order.notes}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>



          {/* Order Details */}
          <Card>
            <CardHeader>
              <CardTitle>Order Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Order Date</p>
                  <p className="text-sm text-gray-600">
                    {new Date(order.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Order Number</p>
                  <p className="text-sm text-gray-600">{order.orderNumber}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Payment Method</p>
                  <p className="text-sm text-gray-600">
                    {order.paymentProof?.notes?.includes('Payment Method:')
                      ? order.paymentProof.notes.replace('Payment Method: ', '')
                      : 'Not specified'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
