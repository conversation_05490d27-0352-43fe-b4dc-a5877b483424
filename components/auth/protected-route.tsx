"use client";

import { useSession } from "@/lib/auth-client";
import { User, UserRole } from "@/utils/types";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircleIcon } from "lucide-react";
import { getUserById } from "@/actions/userActions";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  fallbackUrl?: string;
}

export function ProtectedRoute({
  children,
  requiredRole,
  fallbackUrl = "/sign-in",
}: ProtectedRouteProps) {
  const { data: session, isPending, error } = useSession();
  const router = useRouter();
  const [role, setRole] = useState<UserRole | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getUserDetails = async () => {
      if (session) {
        const user = await getUserById(session.user.id);
        if (user.success && user.data) {
          // Update user in session
          session.user = user.data;
          setRole(user.data.role);
          setLoading(false);
        }
      }
    };
    if (!isPending && !session) {
      setLoading(false);
      router.push(fallbackUrl);
    }
    getUserDetails();
  }, [session, isPending, requiredRole, router, fallbackUrl]);

  if (error) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <Alert variant="destructive">
          <AlertCircleIcon />
          <AlertTitle>Authentication Error</AlertTitle>
          <AlertDescription>
            <p>{error.message}</p>
            <p>Please try again. If error persists, please contact support</p>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!session && (!isPending || !loading)) {
    router.push("/sign-in");
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (isPending && !session) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (!session || loading) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (requiredRole && role !== requiredRole && role !== UserRole.ADMIN) {
    return (
      <div className="w-[90%] md:w-[50%] h-screen flex items-center justify-center">
        <Alert variant="destructive">
          <AlertCircleIcon />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            <p>You don't have permission to access this page.</p>
            <p>Required role: {requiredRole}</p>
            <p>your role: {role}</p>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (session && requiredRole && role !== requiredRole) {
    // If user doesn't have required role, redirect to dashboard
    router.push("/dashboard");
  }

  return <>{children}</>;
}

// Convenience components for specific roles
export function AdminRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>{children}</ProtectedRoute>
  );
}

export function UserRoute({ children }: { children: React.ReactNode }) {
  return <ProtectedRoute>{children}</ProtectedRoute>;
}
