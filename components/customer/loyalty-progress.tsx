"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Gift, 
  Star, 
  CheckCircle, 
  Clock,
  Copy,
  Calendar
} from "lucide-react";

interface LoyaltyStatus {
  completedOrders: number;
  ordersRemaining: number;
  hasLoyaltyDiscount: boolean;
  loyaltyDiscount?: {
    code: string;
    isActive: boolean;
    validUntil: string | null;
    isUsed: boolean;
  };
}

interface LoyaltyProgressProps {
  userId: string;
}

export default function LoyaltyProgress({ userId }: LoyaltyProgressProps) {
  const [loyaltyStatus, setLoyaltyStatus] = useState<LoyaltyStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [copiedCode, setCopiedCode] = useState(false);

  useEffect(() => {
    fetchLoyaltyStatus();
  }, [userId]);

  const fetchLoyaltyStatus = async () => {
    try {
      const response = await fetch(`/api/customer/loyalty-status`);
      const data = await response.json();
      if (data.success) {
        setLoyaltyStatus(data.data);
      }
    } catch (error) {
      console.error('Error fetching loyalty status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyCode = () => {
    if (loyaltyStatus?.loyaltyDiscount?.code) {
      navigator.clipboard.writeText(loyaltyStatus.loyaltyDiscount.code);
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-2 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!loyaltyStatus) {
    return null;
  }

  const progressPercentage = (loyaltyStatus.completedOrders / 5) * 100;
  const isEligible = loyaltyStatus.completedOrders >= 5;

  return (
    <Card className="border-2 border-gradient-to-r from-purple-200 to-pink-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Star className="h-5 w-5 text-yellow-500" />
          Loyalty Rewards Program
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Orders completed</span>
            <span className="font-semibold">
              {loyaltyStatus.completedOrders} / 5
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          <div className="text-xs text-gray-500">
            {loyaltyStatus.ordersRemaining > 0 
              ? `${loyaltyStatus.ordersRemaining} more orders to earn M150 discount`
              : "Congratulations! You've earned your loyalty reward!"
            }
          </div>
        </div>

        {/* Reward Status */}
        {isEligible && loyaltyStatus.hasLoyaltyDiscount && loyaltyStatus.loyaltyDiscount && (
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
            <div className="flex items-center gap-2 mb-3">
              <Gift className="h-5 w-5 text-green-600" />
              <span className="font-semibold text-green-800">
                Your Loyalty Discount is Ready!
              </span>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-mono text-lg font-bold text-gray-900">
                    {loyaltyStatus.loyaltyDiscount.code}
                  </div>
                  <div className="text-sm text-gray-600">M150 off your next order</div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyCode}
                  className="flex items-center gap-1"
                >
                  {copiedCode ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                  {copiedCode ? "Copied!" : "Copy"}
                </Button>
              </div>

              <div className="flex items-center gap-4 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <Badge variant={loyaltyStatus.loyaltyDiscount.isActive ? "default" : "secondary"}>
                    {loyaltyStatus.loyaltyDiscount.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
                
                {loyaltyStatus.loyaltyDiscount.isUsed && (
                  <div className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    <span>Used</span>
                  </div>
                )}
                
                {loyaltyStatus.loyaltyDiscount.validUntil && (
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>
                      Valid until {new Date(loyaltyStatus.loyaltyDiscount.validUntil).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Progress Milestones */}
        <div className="grid grid-cols-5 gap-1">
          {[1, 2, 3, 4, 5].map((milestone) => (
            <div key={milestone} className="text-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold mx-auto mb-1 ${
                loyaltyStatus.completedOrders >= milestone
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-200 text-gray-500'
              }`}>
                {loyaltyStatus.completedOrders >= milestone ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  milestone
                )}
              </div>
              <div className="text-xs text-gray-500">
                {milestone === 5 ? 'Reward!' : `Order ${milestone}`}
              </div>
            </div>
          ))}
        </div>

        {/* Next Steps */}
        {loyaltyStatus.ordersRemaining > 0 && (
          <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
            <div className="flex items-center gap-2 text-blue-800">
              <Clock className="h-4 w-4" />
              <span className="text-sm font-medium">
                Complete {loyaltyStatus.ordersRemaining} more order{loyaltyStatus.ordersRemaining > 1 ? 's' : ''} to unlock M150 discount!
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
