"use client";

import { useCart } from "@/contexts/cart-context";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart, X, ArrowRight } from "lucide-react";
import Link from "next/link";
import { formatPrice, getEffectivePrice, calculateDeliveryFee } from "@/lib/product-utils";

interface MiniCartProps {
  onClose?: () => void;
}

export default function MiniCart({ onClose }: MiniCartProps) {
  const { state: cartState, removeItem } = useCart();

  if (cartState.isLoading) {
    return (
      <Card className="w-80 max-w-[calc(100vw-1rem)] sm:max-w-none">
        <CardContent className="p-4">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (cartState.items.length === 0) {
    return (
      <Card className="w-80 max-w-[calc(100vw-1rem)] sm:max-w-none">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Cart
            </span>
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <ShoppingCart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-gray-500 mb-4">Your cart is empty</p>
          <Link href="/products" onClick={onClose}>
            <Button size="sm">
              Start Shopping
            </Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  const subtotal = cartState.totalPrice;
  const deliveryInfo = calculateDeliveryFee("Maseru", subtotal);
  const deliveryCost = deliveryInfo.fee;
  const total = subtotal + deliveryCost;

  return (
    <Card className="w-96 max-w-[calc(100vw-1rem)] sm:max-w-none">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Cart ({cartState.totalItems})
          </span>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Cart Items */}
        <div className="max-h-64 overflow-y-auto space-y-3">
          {cartState.items.slice(0, 3).map((item) => {
            const effectivePrice = getEffectivePrice(item.product);
            
            return (
              <div key={item.id} className="flex gap-3 p-2 rounded-lg hover:bg-gray-50">
                <div className="flex-shrink-0">
                  <img
                    src={item.product.images[0] || "/placeholder.png"}
                    alt={item.product.name}
                    className="w-12 h-12 object-cover rounded"
                  />
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium truncate">
                    {item.product.name}
                  </h4>
                  <div className="flex items-center gap-1 mt-1">
                    {item.size && (
                      <Badge variant="outline" className="text-xs px-1 py-0">
                        {item.size}
                      </Badge>
                    )}
                    {item.color && (
                      <Badge variant="outline" className="text-xs px-1 py-0">
                        {item.color}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-sm text-gray-600">
                      Qty: {item.quantity}
                    </span>
                    <span className="text-sm font-medium">
                      {formatPrice(effectivePrice * item.quantity)}
                    </span>
                  </div>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem(item.id)}
                  className="text-gray-400 hover:text-red-500 p-1 h-auto"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            );
          })}
          
          {cartState.items.length > 3 && (
            <div className="text-center text-sm text-gray-500 py-2">
              +{cartState.items.length - 3} more items
            </div>
          )}
        </div>

        <Separator />

        {/* Summary */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Subtotal</span>
            <span>{formatPrice(subtotal)}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span>Delivery</span>
            <span className={deliveryInfo.isFree ? "text-green-600" : ""}>
              {deliveryInfo.isFree ? "Free" : formatPrice(deliveryCost)}
            </span>
          </div>
          
          {deliveryInfo.reason && (
            <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded mt-1">{deliveryInfo.reason}</div>
          )}
        </div>

        <Separator />
        
        <div className="flex justify-between font-semibold">
          <span>Total</span>
          <span>{formatPrice(total)}</span>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2">
          <Link href="/checkout" className="w-full" onClick={onClose}>
            <Button size="sm" className="w-full">
              Checkout
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          
          <Link href="/cart" className="w-full" onClick={onClose}>
            <Button variant="outline" size="sm" className="w-full">
              View Cart
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
