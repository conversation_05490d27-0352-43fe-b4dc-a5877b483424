"use client";
import { useEffect } from "react";

export default function ReferralCodeListener() {
  useEffect(() => {
    if (typeof window === "undefined") return;
    const url = new URL(window.location.href);
    const ref = url.searchParams.get("ref");
    const discount = url.searchParams.get("discount");
    
    if (ref) {
      // Set referral code cookie for 30 days
      document.cookie = `referral_code=${ref}; path=/; max-age=${60 * 60 * 24 * 30}`;
    }
    
    if (discount) {
      // Set discount code cookie for 30 days
      document.cookie = `discount_code=${discount}; path=/; max-age=${60 * 60 * 24 * 30}`;
    }
  }, []);
  return null;
} 