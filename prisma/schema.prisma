generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                         String                      @id
  name                       String
  email                      String                      @unique
  emailVerified              Boolean
  image                      String?
  createdAt                  DateTime
  updatedAt                  DateTime
  role                       UserRole                    @default(USER)
  isPartner                  Boolean                     @default(false)
  salesPartner               SalesPartner?
  accounts                   Account[]
  cartItems                  CartItem[]
  contactMessages            ContactMessage[]
  appliedDiscounts           DiscountLog[]
  layBuyCancellationRequests LayBuyCancellationRequest[]
  layBuyOrders               LayBuyOrder[]
  noticeRead                 NoticeRead[]
  orders                     Order[]
  sessions                   Session[]

  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model Category {
  id          String        @id @default(cuid())
  name        String        @unique
  description String?
  image       String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  isActive    Boolean       @default(true)
  DiscountLog DiscountLog[]
  products    Product[]

  @@map("category")
}

model Product {
  id                 String            @id @default(cuid())
  name               String
  description        String?
  price              Float
  discountedPrice    Float?
  brand              String
  categoryId         String
  images             String[]
  sizes              String[]
  colors             String[]
  stock              Int               @default(0)
  isActive           Boolean           @default(true)
  rating             Float             @default(0)
  reviewCount        Int               @default(0)
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  costPrice          Float?
  costPriceUpdatedAt DateTime?
  costPriceUpdatedBy String?
  feesUpdatedAt      DateTime?
  feesUpdatedBy      String?
  lateCollectionFee  Float?
  shippingFee        Float?
  totalCost          Float?
  aiAnalysis         Json?
  enhancedBy         String?
  enhancementStatus  String?
  imageAngles        Json?
  imageSource        String?
  lastEnhancedAt     DateTime?
  qualityScore       Float?
  cartItems          CartItem[]
  layBuyOrderItems   LayBuyOrderItem[]
  orderItems         OrderItem[]
  category           Category          @relation(fields: [categoryId], references: [id])
  reviews            Review[]

  @@map("product")
}

model Review {
  id        String   @id @default(cuid())
  rating    Int
  comment   String?
  productId String
  userId    String
  userName  String
  userEmail String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("review")
}

model CartItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  quantity  Int      @default(1)
  size      String?
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, size, color])
  @@map("cart_item")
}

model Order {
  id                String           @id @default(cuid())
  userId            String
  orderNumber       String           @unique
  status            OrderStatus      @default(PENDING)
  totalAmount       Float
  discountAmount    Float            @default(0)
  discountCodeId    String?
  shippingAddress   String
  phoneNumber       String
  notes             String?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  adminNotes        String?
  delvaNotified     Boolean          @default(false)
  delvaNotifiedAt   DateTime?
  totalCostPrice    Float?
  totalProfit       Float?
  trackingNumber    String?
  trackingUrl       String?
  deliveredAt       DateTime?
  deliveryFee       Float?
  deliveryFeePaid   Boolean          @default(false)
  deliveryFeePaidAt DateTime?
  isBulkDelivery    Boolean          @default(false)
  customerLatitude  Float?
  customerLongitude Float?
  locationEnabled   Boolean          @default(false)
  locationUpdatedAt DateTime?
  deliveryRecords   DeliveryRecord[]
  discountCode      DiscountCode?    @relation(fields: [discountCodeId], references: [id])
  user              User             @relation(fields: [userId], references: [id])
  orderItems        OrderItem[]
  paymentProof      PaymentProof?

  @@map("order")
}

model OrderItem {
  id                String   @id @default(cuid())
  orderId           String
  productId         String
  quantity          Int
  price             Float
  size              String?
  color             String?
  createdAt         DateTime @default(now())
  costPrice         Float?
  profit            Float?
  lateCollectionFee Float?
  shippingFee       Float?
  totalCost         Float?
  order             Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product           Product  @relation(fields: [productId], references: [id])

  @@map("order_item")
}

model DiscountCode {
  id           String        @id @default(cuid())
  code         String        @unique
  description  String?
  type         DiscountType
  value        Float
  minAmount    Float?
  maxUses      Int?
  usedCount    Int           @default(0)
  isActive     Boolean       @default(true)
  validFrom    DateTime      @default(now())
  validUntil   DateTime?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  discountLogs DiscountLog[]
  orders       Order[]

  @@map("discount_code")
}

model PaymentProof {
  id         String        @id @default(cuid())
  orderId    String        @unique
  imageUrl   String
  status     PaymentStatus @default(PENDING)
  notes      String?
  verifiedBy String?
  verifiedAt DateTime?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  order      Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("payment_proof")
}

model Notice {
  id         String       @id @default(cuid())
  title      String
  content    String
  type       NoticeType   @default(INFO)
  isActive   Boolean      @default(true)
  priority   Int          @default(0)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  noticeRead NoticeRead[]

  @@map("notice")
}

model Testimonial {
  id        String   @id @default(cuid())
  name      String
  content   String
  rating    Int
  image     String?
  position  String?
  isActive  Boolean  @default(true)
  priority  Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("testimonial")
}

model ContactMessage {
  id         String        @id @default(cuid())
  userId     String?
  name       String
  email      String
  subject    String
  message    String
  status     MessageStatus @default(UNREAD)
  adminNotes String?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  readAt     DateTime?
  user       User?         @relation(fields: [userId], references: [id])

  @@map("contact_message")
}

model DiscountLog {
  id               String        @id @default(cuid())
  type             DiscountType
  value            Float
  categoryId       String?
  productsAffected Int
  appliedById      String
  createdAt        DateTime      @default(now())
  discountCodeId   String?
  appliedBy        User          @relation(fields: [appliedById], references: [id])
  category         Category?     @relation(fields: [categoryId], references: [id])
  DiscountCode     DiscountCode? @relation(fields: [discountCodeId], references: [id])

  @@map("discount_log")
}

model Settings {
  id                       String   @id @default(cuid())
  primaryColor             String   @default("#3b82f6")
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt
  defaultDeliveryFee       Float    @default(90)
  defaultLateCollectionFee Float    @default(10)
  defaultShippingFee       Float    @default(100)
  bulkDeliveryThreshold    Int      @default(5)
  defaultBulkDeliveryFee   Float    @default(60)
}

model NoticeRead {
  id       String   @id @default(cuid())
  userId   String
  noticeId String
  readAt   DateTime @default(now())
  notice   Notice   @relation(fields: [noticeId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, noticeId])
  @@map("notice_read")
}

model LayBuyOrder {
  id                   String                      @id @default(cuid())
  userId               String
  orderNumber          String                      @unique
  status               LayBuyStatus                @default(ACTIVE)
  totalAmount          Float
  upfrontAmount        Float?
  remainingAmount      Float?
  amountPaid           Float?                      @default(0)
  dueDate              DateTime?
  gracePeriodEnd       DateTime?
  shippingAddress      String
  phoneNumber          String
  notes                String?
  adminNotes           String?
  cancelledAt          DateTime?
  completedAt          DateTime?
  forfeitedAt          DateTime?
  refundAmount         Float?
  createdAt            DateTime                    @default(now())
  updatedAt            DateTime                    @updatedAt
  cancellationRequests LayBuyCancellationRequest[]
  user                 User                        @relation(fields: [userId], references: [id])
  orderItems           LayBuyOrderItem[]
  payments             LayBuyPayment[]
  reminders            LayBuyReminder[]

  @@map("lay_buy_order")
}

model LayBuyOrderItem {
  id            String      @id @default(cuid())
  layBuyOrderId String
  productId     String
  quantity      Int
  price         Float?
  size          String?
  color         String?
  createdAt     DateTime?   @default(now())
  layBuyOrder   LayBuyOrder @relation(fields: [layBuyOrderId], references: [id], onDelete: Cascade)
  product       Product     @relation(fields: [productId], references: [id])

  @@map("lay_buy_order_item")
}

model LayBuyPayment {
  id            String             @id @default(cuid())
  layBuyOrderId String
  amount        Float?
  paymentType   LayBuyPaymentType?
  paymentMethod String?
  paymentProof  String?
  status        PaymentStatus?     @default(PENDING)
  notes         String?
  verifiedBy    String?
  verifiedAt    DateTime?
  createdAt     DateTime?          @default(now())
  updatedAt     DateTime?          @updatedAt
  layBuyOrder   LayBuyOrder        @relation(fields: [layBuyOrderId], references: [id], onDelete: Cascade)

  @@map("lay_buy_payment")
}

model LayBuyReminder {
  id            String        @id @default(cuid())
  layBuyOrderId String
  weekNumber    Int?
  sentAt        DateTime?     @default(now())
  emailSent     Boolean?      @default(false)
  smsSent       Boolean?      @default(false)
  reminderType  ReminderType?
  createdAt     DateTime?     @default(now())
  layBuyOrder   LayBuyOrder   @relation(fields: [layBuyOrderId], references: [id], onDelete: Cascade)

  @@map("lay_buy_reminder")
}

model LayBuyCancellationRequest {
  id            String                     @id @default(cuid())
  layBuyOrderId String
  requestedById String
  reason        String?
  status        CancellationRequestStatus? @default(PENDING)
  refundAmount  Float?
  adminNotes    String?
  processedBy   String?
  processedAt   DateTime?
  createdAt     DateTime?                  @default(now())
  updatedAt     DateTime?                  @updatedAt
  layBuyOrder   LayBuyOrder                @relation(fields: [layBuyOrderId], references: [id], onDelete: Cascade)
  requestedBy   User                       @relation(fields: [requestedById], references: [id])

  @@map("lay_buy_cancellation_request")
}

model DeliveryRecord {
  id                    String          @id @default(cuid())
  orderId               String
  partner               DeliveryPartner @default(DELVA)
  status                DeliveryStatus  @default(SCHEDULED)
  trackingNumber        String?         @unique
  customerName          String
  customerPhone         String
  customerEmail         String
  deliveryAddress       String
  items                 Json
  totalAmount           Float
  deliveryFee           Float
  isBulkDelivery        Boolean         @default(false)
  preferredDeliveryDate DateTime?
  specialInstructions   String?
  estimatedDeliveryDate DateTime?
  scheduledAt           DateTime        @default(now())
  pickedUpAt            DateTime?
  deliveredAt           DateTime?
  lastUpdate            DateTime?
  notes                 String?
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt
  order                 Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("delivery_record")
}

model SalesPartner {
  id               String          @id @default(cuid())
  name             String
  surname          String
  email            String          @unique
  cellNumber       String
  otherCellNumber  String?
  referralCode     String          @unique
  isActive         Boolean         @default(true)
  commissionEarned Float           @default(0)
  bonusPaid        Float           @default(0)
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  discountAmount   Float           @default(0)
  discountCode     String          @unique
  userId           String?         @unique
  referralOrders   ReferralOrder[]
  user             User?           @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model ReferralOrder {
  id           String       @id @default(cuid())
  orderId      String
  partnerId    String
  customerName String
  orderValue   Float
  commission   Float
  createdAt    DateTime     @default(now())
  partner      SalesPartner @relation(fields: [partnerId], references: [id])
}

model OrderValidation {
  id             String   @id @default(cuid())
  orderId        String
  originalTotal  Decimal
  correctedTotal Decimal
  discrepancy    Decimal
  correctionType String
  details        Json?
  correctedBy    String?
  createdAt      DateTime @default(now())

  @@map("order_validation")
}

model ImageEnhancementLog {
  id               String    @id @default(cuid())
  productId        String
  originalImageUrl String
  enhancedImageUrl String?
  enhancementType  String
  status           String
  errorMessage     String?
  processingTime   Int?
  qualityScore     Float?
  triggeredBy      String?
  createdAt        DateTime  @default(now())
  completedAt      DateTime?

  @@map("image_enhancement_log")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  PAID
}

enum MessageStatus {
  UNREAD
  READ
  REPLIED
  RESOLVED
}

enum UserRole {
  USER
  ADMIN
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum PaymentStatus {
  PENDING
  VERIFIED
  REJECTED
}

enum NoticeType {
  INFO
  WARNING
  SUCCESS
  ERROR
}

enum LayBuyStatus {
  ACTIVE
  COMPLETED
  CANCELLED
  FORFEITED
  REFUNDED
}

enum LayBuyPaymentType {
  UPFRONT
  INSTALLMENT
  COMPLETION
}

enum ReminderType {
  WEEKLY
  URGENT
  GRACE_PERIOD
  FINAL_NOTICE
}

enum CancellationRequestStatus {
  PENDING
  APPROVED
  REJECTED
  PROCESSED
}

enum DeliveryPartner {
  DELVA
  SELF_DELIVERY
}

enum DeliveryStatus {
  SCHEDULED
  CONFIRMED
  PICKED_UP
  IN_TRANSIT
  OUT_FOR_DELIVERY
  DELIVERED
  FAILED
  RETURNED
  CANCELLED
}
