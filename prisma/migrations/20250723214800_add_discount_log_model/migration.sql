-- CreateEnum
CREATE TYPE "DiscountType" AS ENUM ('PERCENTAGE', 'FIXED_AMOUNT');

-- CreateTable
CREATE TABLE "discount_log" (
    "id" TEXT NOT NULL,
    "type" "DiscountType" NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "categoryId" TEXT,
    "productsAffected" INTEGER NOT NULL,
    "appliedById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "discount_log_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "discount_log" ADD CONSTRAINT "discount_log_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "category"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "discount_log" ADD CONSTRAINT "discount_log_appliedById_fkey" FOREIGN KEY ("appliedById") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
