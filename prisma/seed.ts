import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Clear existing products to avoid issues with non-unique names
  await prisma.product.deleteMany({});
  console.log('🧹 Cleared existing products');

  // Create categories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { name: 'Sneakers' },
      update: {},
      create: {
        name: 'Sneakers',
        description: 'Premium sneakers and athletic shoes',
        isActive: true,
      },
    }),
    prisma.category.upsert({
      where: { name: 'Fashionwear' },
      update: {},
      create: {
        name: 'Fashionwear',
        description: 'Trendy fashionwear and apparel',
        isActive: true,
      },
    }),
    prisma.category.upsert({
      where: { name: 'Accessories' },
      update: {},
      create: {
        name: 'Accessories',
        description: 'Accessories for watches and similar items',
        isActive: true,
      },
    }),
    prisma.category.upsert({
      where: { name: 'Headwear' },
      update: {},
      create: {
        name: 'Headwear',
        description: 'Caps, hats, and other headwear',
        isActive: true,
      },
    }),
    prisma.category.upsert({
      where: { name: 'Intimates' },
      update: {},
      create: {
        name: 'Intimates',
        description: 'Intimate wear and undergarments',
        isActive: true,
      },
    }),
  ]);

  console.log('✅ Categories created');

  // Create sample products
  const products = [
    {
      name: 'Air Jordan 1 Retro High',
      description: 'Classic basketball shoe with premium leather construction',
      price: 170.00,
      discountedPrice: 150.00,
      brand: 'Nike',
      categoryId: categories[0].id, // Sneakers
      images: ['https://images.unsplash.com/photo-1525547719571-a2d4ac8945e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1964&q=80'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Black/Red', 'White/Black'],
      stock: 50,
      isActive: true,
      rating: 4.8,
      reviewCount: 124,
    },
    {
      name: 'Adidas Ultraboost 22',
      description: 'Premium running shoe with responsive Boost midsole',
      price: 190.00,
      brand: 'Adidas',
      categoryId: categories[0].id, // Sneakers
      images: ['https://images.unsplash.com/photo-1542291026-7eec264c27ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Core Black', 'Cloud White', 'Solar Red'],
      stock: 75,
      isActive: true,
      rating: 4.6,
      reviewCount: 89,
    },
    // New Sample Products
    {
      name: 'Classic Denim Jacket',
      description: 'A timeless denim jacket for any occasion.',
      price: 89.99,
      brand: 'Levi\'s',
      categoryId: categories[1].id, // Fashionwear
      images: ['https://images.unsplash.com/photo-1543076499-8f4b0d71f623?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80'],
      sizes: ['S', 'M', 'L', 'XL'],
      colors: ['Blue', 'Black'],
      stock: 40,
      isActive: true,
      rating: 4.7,
      reviewCount: 55,
    },
    {
      name: 'Leather Strap Watch',
      description: 'Elegant watch with a genuine leather strap.',
      price: 250.00,
      brand: 'Fossil',
      categoryId: categories[2].id, // Accessories
      images: ['https://images.unsplash.com/photo-1524805444758-089113d48a6d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1888&q=80'],
      sizes: [],
      colors: ['Brown', 'Black'],
      stock: 25,
      isActive: true,
      rating: 4.9,
      reviewCount: 98,
    },
    {
      name: 'Classic Baseball Cap',
      description: 'A comfortable and stylish cap for everyday wear.',
      price: 29.99,
      brand: 'New Era',
      categoryId: categories[3].id, // Headwear
      images: ['https://images.unsplash.com/photo-1521119989659-a83eee488004?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1923&q=80'],
      sizes: ['One Size'],
      colors: ['Navy', 'Red', 'Black'],
      stock: 60,
      isActive: true,
      rating: 4.5,
      reviewCount: 78,
    },
    {
      name: 'Silk Boxer Shorts',
      description: 'Luxurious and comfortable silk boxer shorts.',
      price: 45.00,
      brand: 'Calvin Klein',
      categoryId: categories[4].id, // Intimates
      images: ['https://images.unsplash.com/photo-1615111584483-355990264010?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80'],
      sizes: ['S', 'M', 'L'],
      colors: ['Black', 'White', 'Gray'],
      stock: 50,
      isActive: true,
      rating: 4.8,
      reviewCount: 45,
    },
  ];

  // Using createMany for efficiency
  await prisma.product.createMany({
    data: products,
  });

  console.log('✅ Products created');

  // Create discount codes
  await prisma.discountCode.createMany({
    data: [
      {
        code: 'WELCOME10',
        description: 'Welcome discount for new customers',
        type: 'PERCENTAGE',
        value: 10,
        minAmount: 50,
        maxUses: 100,
        isActive: true,
        validUntil: new Date('2025-12-31'),
      },
      {
        code: 'SUMMER25',
        description: 'Summer sale discount',
        type: 'FIXED_AMOUNT',
        value: 25,
        minAmount: 100,
        maxUses: 50,
        isActive: true,
        validUntil: new Date('2025-08-31'),
      },
    ],
  });

  console.log('✅ Discount codes created');

  // Create default settings
  await prisma.settings.upsert({
    where: { id: 'default' },
    update: {},
    create: {
      id: 'default',
      primaryColor: '#3b82f6',
    },
  });

  console.log('✅ Settings created');

  // Create sample notices
  await prisma.notice.createMany({
    data: [
      {
        title: 'Welcome to RIVV Premium Sneakers!',
        content: 'Thank you for visiting our store. Check out our latest collection of premium sneakers.',
        type: 'INFO',
        isActive: true,
        priority: 1,
      },
      {
        title: 'Free Shipping',
        content: 'Free shipping on orders over $100. Limited time offer!',
        type: 'SUCCESS',
        isActive: true,
        priority: 2,
      },
    ],
  });

  console.log('✅ Notices created');

  console.log('🎉 Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
