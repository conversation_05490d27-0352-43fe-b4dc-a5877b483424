"use server";

import prisma from "@/lib/prisma";
import { DiscountCode, DiscountType } from "@/utils/types";

/**
 * Get all discount codes with optional filtering
 */
export async function getDiscountCodes(filters?: {
  search?: string;
  isActive?: boolean;
  type?: DiscountType;
}) {
  try {
    const where: any = {};

    if (filters?.search) {
      where.code = {
        contains: filters.search,
        mode: "insensitive"
      };
    }

    if (filters?.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters?.type) {
      where.type = filters.type;
    }

    const discountCodes = await prisma.discountCode.findMany({
      where,
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      },
      orderBy: { createdAt: "desc" }
    });

    return { success: true, data: discountCodes };
  } catch (error) {
    console.error("Error fetching discount codes:", error);
    return { success: false, error: "Failed to fetch discount codes" };
  }
}

/**
 * Get a single discount code by ID
 */
export async function getDiscountCodeById(discountCodeId: string) {
  try {
    const discountCode = await prisma.discountCode.findUnique({
      where: { id: discountCodeId },
      include: {
        orders: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          },
          orderBy: { createdAt: "desc" }
        },
        _count: {
          select: {
            orders: true
          }
        }
      }
    });

    if (!discountCode) {
      return { success: false, error: "Discount code not found" };
    }

    return { success: true, data: discountCode };
  } catch (error) {
    console.error("Error fetching discount code:", error);
    return { success: false, error: "Failed to fetch discount code" };
  }
}

/**
 * Get discount code by code string
 */
export async function getDiscountCodeByCode(code: string) {
  try {
    const discountCode = await prisma.discountCode.findUnique({
      where: { code: code.toUpperCase() }
    });

    if (!discountCode) {
      return { success: false, error: "Discount code not found" };
    }

    // Check if code is active
    if (!discountCode.isActive) {
      return { success: false, error: "Discount code is not active" };
    }

    // Check if code is not yet valid
    if (discountCode.validFrom && new Date() < discountCode.validFrom) {
      return { success: false, error: "Discount code is not yet active" };
    }

    // Check if code has expired
    if (discountCode.validUntil && new Date() > discountCode.validUntil) {
      return { success: false, error: "Discount code has expired" };
    }

    // Check usage limit
    if (discountCode.maxUses && discountCode.usedCount >= discountCode.maxUses) {
      return { success: false, error: "Discount code usage limit reached" };
    }

    return { success: true, data: discountCode };
  } catch (error) {
    console.error("Error fetching discount code by code:", error);
    return { success: false, error: "Failed to fetch discount code" };
  }
}

/**
 * Create a new discount code
 */
export async function createDiscountCode(discountCodeData: {
  code: string;
  type: DiscountType;
  value: number;
  isActive?: boolean;
  maxUses?: number;
  validUntil?: Date;
  usageLimit?: number;
}) {
  try {
    // Check if code already exists
    const existingCode = await prisma.discountCode.findUnique({
      where: { code: discountCodeData.code.toUpperCase() }
    });

    if (existingCode) {
      return { success: false, error: "Discount code already exists" };
    }

    // Prepare data for Prisma
    const { usageLimit, validUntil, ...rest } = discountCodeData;
    const data: DiscountCode = {
      ...rest,
      code: discountCodeData.code.toUpperCase(),
      isActive: discountCodeData.isActive ?? true,
      // usedCount: 0,
      maxUses: usageLimit !== undefined ? usageLimit : undefined,
      validUntil: validUntil ? new Date(validUntil) : undefined
    };
    const discountCode = await prisma.discountCode.create({
      data,
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    });

    return { success: true, data: discountCode };
  } catch (error) {
    console.error("Error creating discount code:", error);
    return { success: false, error: "Failed to create discount code" };
  }
}

/**
 * Update a discount code
 */
export async function updateDiscountCode(discountCodeId: string, discountCodeData: {
  code?: string;
  type?: DiscountType;
  value?: number;
  isActive?: boolean;
  maxUses?: number;
  validUntil?: Date;
}) {
  try {
    // If updating code, check if it already exists
    if (discountCodeData.code) {
      const existingCode = await prisma.discountCode.findFirst({
        where: {
          code: discountCodeData.code.toUpperCase(),
          id: {
            not: discountCodeId
          }
        }
      });

      if (existingCode) {
        return { success: false, error: "Discount code already exists" };
      }
    }

    const updateData = { ...discountCodeData };
    if (updateData.code) {
      updateData.code = updateData.code.toUpperCase();
    }

    const discountCode = await prisma.discountCode.update({
      where: { id: discountCodeId },
      data: {
        ...updateData,
        updatedAt: new Date()
      },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    });

    return { success: true, data: discountCode };
  } catch (error) {
    console.error("Error updating discount code:", error);
    return { success: false, error: "Failed to update discount code" };
  }
}

/**
 * Delete a discount code
 */
export async function deleteDiscountCode(discountCodeId: string) {
  try {
    // Check if discount code has been used in orders
    const discountCode = await prisma.discountCode.findUnique({
      where: { id: discountCodeId },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    });

    if (!discountCode) {
      return { success: false, error: "Discount code not found" };
    }

    if (discountCode._count.orders > 0) {
      // If discount code has been used, just mark as inactive instead of deleting
      const updatedDiscountCode = await prisma.discountCode.update({
        where: { id: discountCodeId },
        data: { isActive: false }
      });
      return { 
        success: true, 
        data: updatedDiscountCode, 
        message: "Discount code marked as inactive due to existing orders" 
      };
    }

    // Delete the discount code if no orders exist
    await prisma.discountCode.delete({
      where: { id: discountCodeId }
    });

    return { success: true, message: "Discount code deleted successfully" };
  } catch (error) {
    console.error("Error deleting discount code:", error);
    return { success: false, error: "Failed to delete discount code" };
  }
}

/**
 * Apply discount code to order
 */
export async function applyDiscountCode(code: string, orderTotal: number) {
  try {
    const result = await getDiscountCodeByCode(code);
    
    if (!result.success || !result.data) {
      return result;
    }

    const discountCode = result.data;
    let discountAmount = 0;

    if (discountCode.type === "PERCENTAGE") {
      discountAmount = (orderTotal * discountCode.value) / 100;
    } else if (discountCode.type === "FIXED_AMOUNT") {
      discountAmount = Math.min(discountCode.value, orderTotal);
    }

    return {
      success: true,
      data: {
        discountCode,
        discountAmount,
        finalTotal: orderTotal - discountAmount
      }
    };
  } catch (error) {
    console.error("Error applying discount code:", error);
    return { success: false, error: "Failed to apply discount code" };
  }
}

/**
 * Increment discount code usage
 */
export async function incrementDiscountCodeUsage(discountCodeId: string) {
  try {
    const discountCode = await prisma.discountCode.update({
      where: { id: discountCodeId },
      data: {
        usedCount: {
          increment: 1
        }
      }
    });

    return { success: true, data: discountCode };
  } catch (error) {
    console.error("Error incrementing discount code usage:", error);
    return { success: false, error: "Failed to increment discount code usage" };
  }
}

/**
 * Get discount code statistics
 */
export async function getDiscountCodeStats() {
  try {
    const [
      totalCodes,
      activeCodes,
      expiredCodes,
      totalUsage,
      percentageCodes,
      fixedAmountCodes
    ] = await Promise.all([
      prisma.discountCode.count(),
      prisma.discountCode.count({ where: { isActive: true } }),
      prisma.discountCode.count({
        where: {
          validUntil: {
            lt: new Date()
          }
        }
      }),
      prisma.discountCode.aggregate({
        _sum: { usedCount: true }
      }),
      prisma.discountCode.count({ where: { type: "PERCENTAGE" } }),
      prisma.discountCode.count({ where: { type: "FIXED_AMOUNT" } })
    ]);

    return {
      success: true,
      data: {
        totalCodes,
        activeCodes,
        expiredCodes,
        totalUsage: totalUsage._sum.usedCount || 0,
        percentageCodes,
        fixedAmountCodes
      }
    };
  } catch (error) {
    console.error("Error fetching discount code stats:", error);
    return { success: false, error: "Failed to fetch discount code statistics" };
  }
}
