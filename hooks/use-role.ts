"use client";

import { useSession } from "@/lib/auth-client";
import { UserRole, User } from "@/utils/types";
import { useEffect, useState } from "react";
import { getUserById } from "@/actions/userActions";

export function useRole() {
  const { data: session, isPending } = useSession();
  const [currentRole, setCurrentRole] = useState<UserRole | null>(null);
  const [userWithRole, setUserWithRole] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchUserRole = async () => {
      if (session?.user && !isLoading) {
        setIsLoading(true);
        try {
          const userResponse = await getUserById(session.user.id);
          if (userResponse.success && userResponse.data) {
            setCurrentRole(userResponse.data.role);
            setUserWithRole(userResponse.data as User);
          }
        } catch (error) {
          console.error("Error fetching user role:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchUserRole();
  }, [session?.user?.id, isLoading]);

  const hasRole = (requiredRole: UserRole): boolean => {
    if (!currentRole) return false;

    // Admin has access to everything
    if (currentRole === UserRole.ADMIN) return true;

    // Check specific role
    return currentRole === requiredRole;
  };

  const isAdmin = (): boolean => {
    return currentRole === UserRole.ADMIN || false;
  };

  const isUser = (): boolean => {
    return currentRole === UserRole.USER || false;
  };

  const getUserRole = (): UserRole | null => {
    return currentRole;
  };

  const isAuthenticated = (): boolean => {
    return !!session?.user;
  };

  return {
    hasRole,
    isAdmin,
    isUser,
    getUserRole,
    isAuthenticated,
    isPending: isPending || isLoading,
    user: userWithRole || session?.user || null,
    currentRole,
  };
}
