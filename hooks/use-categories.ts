'use client';

import { useState, useEffect } from 'react';
import { getCategories } from '@/actions/categoryActions';

interface Category {
  id: string;
  name: string;
  description: string | null;
  image: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  _count: {
    products: number;
  };
}

interface UseCategoriesOptions {
  search?: string;
}

export function useCategories(options: UseCategoriesOptions = {}) {
  const [data, setData] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const result = await getCategories({
          search: options.search,
        });
        
        if (result.success) {
          setData(result.data || []);
        } else {
          setError(result.error || 'Failed to fetch categories');
        }
      } catch (err) {
        setError('An unexpected error occurred');
        console.error('Error in useCategories:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, [options.search]);

  return {
    data,
    isLoading,
    error,
    refetch: () => {
      const fetchCategories = async () => {
        try {
          setIsLoading(true);
          setError(null);
          
          const result = await getCategories({
            search: options.search,
          });
          
          if (result.success) {
            setData(result.data || []);
          } else {
            setError(result.error || 'Failed to fetch categories');
          }
        } catch (err) {
          setError('An unexpected error occurred');
          console.error('Error in useCategories refetch:', err);
        } finally {
          setIsLoading(false);
        }
      };

      fetchCategories();
    }
  };
}
