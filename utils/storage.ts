// Server-side only storage utilities
// This file should only be imported in API routes or server-side code

import fs from 'fs';
import path from 'path';

/**
 * Simple file storage utility for development
 * In production, you should use a proper cloud storage service like S3, Cloudinary, or UploadThing
 */

const UPLOAD_DIR = path.join(process.cwd(), 'public', 'uploads');

// Ensure upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * Upload a buffer to local storage
 * This is a simplified implementation for development
 * In production, replace this with actual cloud storage
 */
export async function uploadToS3(
  buffer: Buffer,
  fileName: string,
  mimeType: string
): Promise<string> {
  try {
    // Sanitize filename
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filePath = path.join(UPLOAD_DIR, sanitizedFileName);
    
    // Write file to disk
    fs.writeFileSync(filePath, buffer);
    
    // Return public URL
    return `/uploads/${sanitizedFileName}`;
  } catch (error) {
    console.error('Error uploading file:', error);
    throw new Error('Failed to upload file');
  }
}

/**
 * Delete a file from storage
 */
export async function deleteFromStorage(fileName: string): Promise<void> {
  try {
    const filePath = path.join(UPLOAD_DIR, fileName);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  } catch (error) {
    console.error('Error deleting file:', error);
    throw new Error('Failed to delete file');
  }
}

/**
 * Check if a file exists in storage
 */
export function fileExists(fileName: string): boolean {
  const filePath = path.join(UPLOAD_DIR, fileName);
  return fs.existsSync(filePath);
}
