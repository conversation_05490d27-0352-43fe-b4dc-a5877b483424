// Order Status Constants
export const ORDER_STATUS = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  CONFIRMED: 'CONFIRMED',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
} as const;

export type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS];

// Lay-Buy Status Constants
export const LAY_BUY_STATUS = {
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  FORFEITED: 'FORFEITED',
  REFUNDED: 'REFUNDED',
} as const;

export type LayBuyStatus = typeof LAY_BUY_STATUS[keyof typeof LAY_BUY_STATUS];

// Payment Status Constants
export const PAYMENT_STATUS = {
  PENDING: 'PENDING',
  VERIFIED: 'VERIFIED',
  REJECTED: 'REJECTED',
} as const;

export type PaymentStatus = typeof PAYMENT_STATUS[keyof typeof PAYMENT_STATUS];

// User Role Constants
export const USER_ROLE = {
  USER: 'USER',
  ADMIN: 'ADMIN',
} as const;

export type UserRole = typeof USER_ROLE[keyof typeof USER_ROLE];

// Contact Message Status Constants
export const MESSAGE_STATUS = {
  UNREAD: 'UNREAD',
  READ: 'READ',
  REPLIED: 'REPLIED',
  RESOLVED: 'RESOLVED',
} as const;

export type MessageStatus = typeof MESSAGE_STATUS[keyof typeof MESSAGE_STATUS];

// Delivery Partner Constants
export const DELIVERY_PARTNER = {
  DELVA: 'DELVA',
  SELF_DELIVERY: 'SELF_DELIVERY',
} as const;

export type DeliveryPartner = typeof DELIVERY_PARTNER[keyof typeof DELIVERY_PARTNER];

// Email Templates
export const EMAIL_TEMPLATES = {
  ORDER_CONFIRMATION: 'ORDER_CONFIRMATION',
  ORDER_STATUS_UPDATE: 'ORDER_STATUS_UPDATE',
  LAY_BUY_CONFIRMATION: 'LAY_BUY_CONFIRMATION',
  LAY_BUY_REMINDER: 'LAY_BUY_REMINDER',
  DELVA_DELIVERY_NOTIFICATION: 'DELVA_DELIVERY_NOTIFICATION',
  CONTACT_CONFIRMATION: 'CONTACT_CONFIRMATION',
  ADMIN_NOTIFICATION: 'ADMIN_NOTIFICATION',
} as const;

// Business Constants
export const BUSINESS_CONFIG = {
  COMPANY_NAME: 'RIVV',
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '+266 6284 4473',
  SUPPORT_WHATSAPP: '+266 6284 4473',
  MPESA_NUMBER: '+266 5316 3354',
  ECOCASH_NUMBER: '+266 6284 4473',
  DELVA_CONTACT: {
    EMAIL: '<EMAIL>',
    PHONE: '+266 56094079',
    WHATSAPP: '+266 56094079',
  },
  LAY_BUY_CONFIG: {
    UPFRONT_PERCENTAGE: 0.6, // 60%
    DURATION_WEEKS: 6,
    GRACE_PERIOD_DAYS: 7,
    LATE_COLLECTION_FEE: 10, // M10
  },
  DELIVERY_CONFIG: {
    DEFAULT_FEE: 90, // M90 per shoe
    BULK_FEE: 60, // M60 per shoe for 5+ shoes
    BULK_THRESHOLD: 5,
    FREE_DELIVERY_THRESHOLD: 500, // M500
  },
} as const;

// Validation Constants
export const VALIDATION_LIMITS = {
  MESSAGE_MIN_LENGTH: 10,
  MESSAGE_MAX_LENGTH: 500,
  PHONE_MIN_LENGTH: 10,
  PHONE_MAX_LENGTH: 15,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  ADDRESS_MIN_LENGTH: 10,
  ADDRESS_MAX_LENGTH: 200,
} as const; 