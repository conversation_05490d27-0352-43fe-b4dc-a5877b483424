const fetch = require('node-fetch');

async function testAPI() {
  try {
    // Test without category filter
    console.log('=== Testing without category filter ===');
    const response1 = await fetch('http://localhost:3001/api/products?sortBy=name&sortOrder=asc&page=1&limit=5');
    const result1 = await response1.json();
    
    if (result1.success) {
      console.log('First 5 products (no category filter):');
      result1.data.data.forEach((p, i) => {
        console.log(`${i+1}. ${p.brand} - ${p.name}`);
      });
    }
    
    // Test with sneakers category filter
    console.log('\n=== Testing with sneakers category filter ===');
    const response2 = await fetch('http://localhost:3001/api/products?sortBy=name&sortOrder=asc&page=1&limit=5&categoryId=e1b2c3d4-5678-9abc-def0-123456789abc');
    const result2 = await response2.json();
    
    if (result2.success) {
      console.log('First 5 products (with sneakers category):');
      result2.data.data.forEach((p, i) => {
        console.log(`${i+1}. ${p.brand} - ${p.name}`);
      });
    }
    
    // Get categories to find the correct sneakers category ID
    console.log('\n=== Getting categories ===');
    const response3 = await fetch('http://localhost:3001/api/categories');
    const result3 = await response3.json();
    
    if (result3.success) {
      console.log('Available categories:');
      result3.data.forEach(cat => {
        console.log(`- ${cat.name} (${cat.id})`);
      });
      
      // Find sneakers category
      const sneakersCategory = result3.data.find(c => c.name.toLowerCase() === 'sneakers');
      if (sneakersCategory) {
        console.log(`\n=== Testing with correct sneakers category ID: ${sneakersCategory.id} ===`);
        const response4 = await fetch(`http://localhost:3001/api/products?sortBy=name&sortOrder=asc&page=1&limit=5&categoryId=${sneakersCategory.id}`);
        const result4 = await response4.json();
        
        if (result4.success) {
          console.log('First 5 products (with correct sneakers category):');
          result4.data.data.forEach((p, i) => {
            console.log(`${i+1}. ${p.brand} - ${p.name}`);
          });
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testAPI();
