"use client";

import { useSession } from "@/lib/auth-client";
import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import AdminPendingPayments from "@/components/admin/lay-buy/admin-pending-payments";
import { User } from "@/utils/types";
import { useEffect, useState } from "react";
import { getUserById } from "@/actions/userActions";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

export default function AdminLayBuyPaymentsPage() {
  return (
    <AdminRoute>
      <AdminLayBuyPaymentsPageContent />
    </AdminRoute>
  );
}

function AdminLayBuyPaymentsPageContent() {
  const { data: session, isPending } = useSession();
  const [userWithRole, setUserWithRole] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // At this point, we know the user is authenticated and is an admin due to AdminRoute
  const user = session!.user;

  useEffect(() => {
    const getUserDetails = async () => {
      if (user) {
        try {
          const userResponse = await getUserById(user.id);
          if (userResponse.success && userResponse.data) {
            setUserWithRole(userResponse.data as User);
          }
        } catch (error) {
          console.error("Error fetching user details:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    getUserDetails();
  }, [user]);

  if (isPending || isLoading || !userWithRole) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  return (
    <AdminLayout user={userWithRole}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Lay-Buy Payment Management</h1>
          <p className="text-gray-600">Review and verify pending Lay-Buy payments</p>
        </div>
        <AdminPendingPayments />
      </div>
    </AdminLayout>
  );
}
