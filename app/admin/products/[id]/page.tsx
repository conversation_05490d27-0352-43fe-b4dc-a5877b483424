"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import AdminLayout from "@/components/admin/admin-layout";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import ProductImageGallery from "@/components/products/product-image-gallery";
import AdminBackButton from "@/components/admin/common/admin-back-button";
import { authClient } from "@/lib/auth-client";
import { Product, User } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import { set } from "better-auth";

export default function AdminViewProductPage() {
  const { id } = useParams();
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [user, setUser] = useState<User | null | undefined>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      setLoading(true);
      setError("");
      try {
        const res = await fetch(`/api/products/${id}`);
        const data = await res.json();
        const userFromDb = await getCurrentUser();

        setUser(userFromDb);

        if (data.success) {
          setProduct(data.data);
        } else {
          setError(data.error || "Product not found");
        }
      } catch (err) {
        setError("Failed to fetch product");
      } finally {
        setLoading(false);
      }
    };
    if (id) fetchProduct();
  }, [id]);

  if (loading) {
    return (
      <div className="flex w-full h-screen justify-center items-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (user) {
    return (
      <AdminLayout user={user}>
        <div className="max-w-4xl mx-auto py-8 px-4">
          <AdminBackButton href="/admin/products" label="Back to Products" />
          <h1 className="text-3xl font-bold mb-4">Product Details</h1>
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <SpinnerCircle4 />
            </div>
          ) : error ? (
            <div className="text-red-600">{error}</div>
          ) : product ? (
            <div className="bg-white border rounded-lg shadow p-6">
              <div className="mb-6">
                <h2 className="text-2xl font-semibold mb-2">{product.name}</h2>
                <p className="text-gray-600 mb-1">Brand: {product.brand}</p>
                <p className="text-gray-600 mb-1">
                  Category: {product.category?.name}
                </p>
                <p className="text-gray-600 mb-1">Price: M{product.price}</p>
                {product.discountedPrice && (
                  <p className="text-gray-600 mb-1">
                    Discounted Price: M{product.discountedPrice}
                  </p>
                )}
                <p className="text-gray-600 mb-1">Stock: {product.stock}</p>
                <p className="text-gray-600 mb-1">
                  Active: {product.isActive ? "Yes" : "No"}
                </p>
                <p className="text-gray-600 mb-1">
                  Created: {new Date(product.createdAt).toLocaleString()}
                </p>
                <p className="text-gray-600 mb-1">
                  Updated: {new Date(product.updatedAt).toLocaleString()}
                </p>
              </div>
              <div className="mb-6">
                <ProductImageGallery
                  images={product.images}
                  productName={product.name}
                />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Description</h3>
                <p className="text-gray-700 whitespace-pre-line">
                  {product.description}
                </p>
              </div>
            </div>
          ) : null}
        </div>
      </AdminLayout>
    );
  }

  router.push("/sign-in");
  return (
    <div className="flex justify-center items-center h-40">
      <SpinnerCircle4 />
    </div>
  );
}
