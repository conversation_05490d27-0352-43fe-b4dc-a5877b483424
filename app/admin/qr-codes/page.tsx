"use client";

import QRCode from "@/components/ui/qr-code";

export default function AdminQRCodesPage() {
  const qrLink = typeof window !== "undefined"
    ? `${window.location.origin}/qr-welcome`
    : "https://rivvsneakers.shop/qr-welcome";

  return (
    <main className="min-h-screen bg-white text-[#1b1f3b] px-4 py-8 flex flex-col items-center">
      <h1 className="text-2xl font-bold mb-2 text-center">QR Codes</h1>
      <p className="mb-6 text-center text-gray-600 max-w-xs">
        This is the main QR code for the Rivv Sneakers welcome page. Download and use it for print, WhatsApp, or flyers. This QR code is always valid and reusable.
      </p>
      <div className="bg-white p-4 rounded-xl shadow-lg mb-4 border border-gray-200">
        <QRCode
          data={qrLink}
          size={220}
          options={{ color: { dark: "#000000", light: "#FFFFFF" }, errorCorrectionLevel: "H", margin: 4 }}
          showDownload={true}
          showRefresh={false}
          alt="Rivv Welcome QR Code"
        />
      </div>
      <div className="text-xs text-gray-500 mt-2 text-center">
        Link: <span className="break-all">{qrLink}</span>
      </div>
    </main>
  );
} 