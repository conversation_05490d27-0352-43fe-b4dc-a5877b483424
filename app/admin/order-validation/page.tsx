"use client";

import { useSession } from "@/lib/auth-client";
import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import ValidationDashboard from "@/components/admin/order-validation/validation-dashboard";
import { User } from "@/utils/types";
import { useEffect, useState } from "react";
import { getCurrentUser } from "@/lib/auth-utils";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

export default function OrderValidationPage() {
  return (
    <AdminRoute>
      <OrderValidationPageContent />
    </AdminRoute>
  );
}

function OrderValidationPageContent() {
  const { data: session, isPending } = useSession();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const currentUser = await getCurrentUser();
        if (currentUser) {
          setUser(currentUser as User);
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      } finally {
        setLoading(false);
      }
    };

    if (session?.user && !isPending) {
      fetchUser();
    } else if (!isPending) {
      setLoading(false);
    }
  }, [session, isPending]);

  if (loading || isPending) {
    return (
      <div className="flex justify-center items-center w-full h-screen">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center w-full h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Access Denied</h2>
          <p className="text-gray-600">You need admin privileges to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout user={user}>
      <ValidationDashboard />
    </AdminLayout>
  );
}
