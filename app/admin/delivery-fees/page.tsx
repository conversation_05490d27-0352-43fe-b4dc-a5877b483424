"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Truck, 
  DollarSign, 
  Package, 
  Calendar,
  Download,
  CheckCircle,
  Clock,
  Users
} from "lucide-react";
import { formatPrice } from "@/lib/product-utils";

interface DeliveryOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  deliveredAt: string;
  deliveryFee: number;
  isBulkDelivery: boolean;
  shoesCount: number;
  deliveryFeePaid: boolean;
  deliveryFeePaidAt: string | null;
}

interface DeliveryStats {
  totalFeesOwed: number;
  totalFeesPaid: number;
  pendingFees: number;
  deliveredOrdersCount: number;
  bulkDeliveries: number;
  regularDeliveries: number;
}

export default function DeliveryFeesPage() {
  const [deliveryOrders, setDeliveryOrders] = useState<DeliveryOrder[]>([]);
  const [stats, setStats] = useState<DeliveryStats>({
    totalFeesOwed: 0,
    totalFeesPaid: 0,
    pendingFees: 0,
    deliveredOrdersCount: 0,
    bulkDeliveries: 0,
    regularDeliveries: 0,
  });
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    fetchDeliveryFees();
  }, []);

  const fetchDeliveryFees = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/delivery-fees');
      if (!response.ok) throw new Error('Failed to fetch delivery fees');
      
      const data = await response.json();

      // Handle API response structure
      const orders = data.success ? data.data.orders : [];
      const stats = data.success ? data.data.stats : {
        totalFeesOwed: 0,
        totalFeesPaid: 0,
        pendingFees: 0,
        deliveredOrdersCount: 0,
        bulkDeliveries: 0,
        regularDeliveries: 0,
      };

      setDeliveryOrders(orders);
      setStats(stats);
    } catch (error) {
      console.error('Error fetching delivery fees:', error);
      setMessage({ type: 'error', text: 'Failed to load delivery fees' });
    } finally {
      setLoading(false);
    }
  };

  const markFeePaid = async (orderId: string) => {
    try {
      const response = await fetch(`/api/admin/delivery-fees/${orderId}/mark-paid`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) throw new Error('Failed to mark fee as paid');

      setMessage({ type: 'success', text: 'Delivery fee marked as paid!' });
      await fetchDeliveryFees(); // Refresh data
    } catch (error) {
      console.error('Error marking fee as paid:', error);
      setMessage({ type: 'error', text: 'Failed to mark fee as paid' });
    }
  };

  const exportDeliveryReport = async () => {
    try {
      setExporting(true);
      const response = await fetch('/api/admin/delivery-fees/export');
      if (!response.ok) throw new Error('Failed to export report');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `delivery-fees-report-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setMessage({ type: 'success', text: 'Report exported successfully!' });
    } catch (error) {
      console.error('Error exporting report:', error);
      setMessage({ type: 'error', text: 'Failed to export report' });
    } finally {
      setExporting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading delivery fees...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Delivery Fee Management</h1>
            <p className="text-gray-600">
              Track delivery fees owed to Delva with automatic bulk pricing (M90 regular, M60 for 5+ shoes).
            </p>
          </div>
          <Button onClick={exportDeliveryReport} disabled={exporting}>
            {exporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </>
            )}
          </Button>
        </div>
      </div>

      {message && (
        <Alert className={`mb-6 ${message.type === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>
          <AlertDescription className={message.type === 'success' ? 'text-green-700' : 'text-red-700'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Fees</p>
                <p className="text-3xl font-bold text-orange-600">M{stats.pendingFees.toLocaleString()}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Fees Owed</p>
                <p className="text-3xl font-bold text-red-600">M{stats.totalFeesOwed.toLocaleString()}</p>
              </div>
              <DollarSign className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Fees Paid</p>
                <p className="text-3xl font-bold text-green-600">M{stats.totalFeesPaid.toLocaleString()}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Delivered Orders</p>
                <p className="text-3xl font-bold text-blue-600">{stats.deliveredOrdersCount}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delivery Type Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Bulk Deliveries (M60/shoe)</p>
                <p className="text-2xl font-bold text-purple-600">{stats.bulkDeliveries}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Regular Deliveries (M90/shoe)</p>
                <p className="text-2xl font-bold text-indigo-600">{stats.regularDeliveries}</p>
              </div>
              <Truck className="h-8 w-8 text-indigo-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delivery Orders List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Delivered Orders & Fees
          </CardTitle>
        </CardHeader>
        <CardContent>
          {deliveryOrders.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Delivered Orders</h3>
              <p className="text-gray-600">No orders have been marked as delivered yet.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {deliveryOrders.map((order) => (
                <div key={order.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4">
                        <div>
                          <h3 className="font-semibold text-gray-900">{order.orderNumber}</h3>
                          <p className="text-sm text-gray-600">{order.customerName}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={order.isBulkDelivery ? "secondary" : "outline"}>
                            {order.isBulkDelivery ? "Bulk Delivery" : "Regular Delivery"}
                          </Badge>
                          <Badge variant="outline">
                            {order.shoesCount} shoe{order.shoesCount !== 1 ? 's' : ''}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {new Date(order.deliveredAt).toLocaleDateString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {formatPrice(order.deliveryFee)}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {order.deliveryFeePaid ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          Paid {order.deliveryFeePaidAt && new Date(order.deliveryFeePaidAt).toLocaleDateString()}
                        </Badge>
                      ) : (
                        <Button
                          size="sm"
                          onClick={() => markFeePaid(order.id)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Mark Paid
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
