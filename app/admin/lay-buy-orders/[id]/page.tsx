"use client";

import { useSession } from "@/lib/auth-client";
import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import AdminLayBuyOrderDetail from "@/components/admin/lay-buy/admin-lay-buy-order-detail";
import { useParams } from "next/navigation";
import { User } from "@/utils/types";
import { useEffect, useState } from "react";
import { getUserById } from "@/actions/userActions";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import AdminBackButton from "@/components/admin/common/admin-back-button";

export default function AdminLayBuyOrderDetailPage() {
  return (
    <AdminRoute>
      <AdminLayBuyOrderDetailPageContent />
    </AdminRoute>
  );
}

function AdminLayBuyOrderDetailPageContent() {
  const { data: session, isPending } = useSession();
  const params = useParams();
  const orderId = params.id as string;
  const [userWithRole, setUserWithRole] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // At this point, we know the user is authenticated and is an admin due to AdminRoute
  const user = session!.user;

  useEffect(() => {
    const getUserDetails = async () => {
      if (user) {
        try {
          const userResponse = await getUserById(user.id);
          if (userResponse.success && userResponse.data) {
            setUserWithRole(userResponse.data as User);
          }
        } catch (error) {
          console.error("Error fetching user details:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    getUserDetails();
  }, [user]);

  if (isPending || isLoading || !userWithRole) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  return (
    <AdminLayout user={userWithRole}>
      <div>
        <AdminBackButton href="/admin/lay-buy-orders" label="Back to Lay-Buy Orders" />
        <AdminLayBuyOrderDetail orderId={orderId} />
      </div>
    </AdminLayout>
  );
}
