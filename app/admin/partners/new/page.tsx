"use client";

import { ProtectedRoute } from '@/components/auth/protected-route';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { PartnerQRCode } from '@/components/ui/qr-code';

function generateReferralCode(name: string, surname: string) {
  if (!name || !surname) return '';
  return (
    name.trim().slice(0, 4).toUpperCase() +
    surname.trim().slice(0, 2).toUpperCase() +
    Math.floor(10 + Math.random() * 90)
  );
}

export default function CreatePartnerPage() {
  const [form, setForm] = useState({
    name: '',
    surname: '',
    email: '',
    cellNumber: '',
    otherCellNumber: '',
    referralCode: '',
    discountAmount: '',
    discountCode: '',
  });
  const [autoGenerated, setAutoGenerated] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleChange = (field: string, value: string) => {
    setForm((prev) => ({ ...prev, [field]: value }));
    if (autoGenerated && (field === 'name' || field === 'surname')) {
      setForm((prev) => ({
        ...prev,
        referralCode: generateReferralCode(
          field === 'name' ? value : prev.name,
          field === 'surname' ? value : prev.surname
        ),
      }));
    }
  };

  const handleAutoGenerate = () => {
    setAutoGenerated(true);
    setForm((prev) => ({
      ...prev,
      referralCode: generateReferralCode(prev.name, prev.surname),
    }));
  };

  const handleManual = () => {
    setAutoGenerated(false);
  };

  const handleQR = () => {
    if (!form.referralCode) return;
    const url = `https://rivvsneakers.shop/?ref=${form.referralCode}&discount=${form.discountCode}`;
    // This will be handled by the QR code component
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/admin/partners', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create partner');
      }

      // Success - redirect to partners list
      router.push('/admin/partners?message=partner_created');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create partner');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProtectedRoute requiredRole="ADMIN">
      <div className="max-w-xl mx-auto py-8 px-2 md:px-0">
        <h1 className="text-2xl font-bold mb-6">Create Sales Partner</h1>
        <form onSubmit={handleSubmit} className="space-y-6 bg-white rounded shadow p-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 font-medium">Name *</label>
              <Input
                required
                value={form.name}
                onChange={e => handleChange('name', e.target.value)}
                placeholder="e.g. Lebohang"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">Surname *</label>
              <Input
                required
                value={form.surname}
                onChange={e => handleChange('surname', e.target.value)}
                placeholder="e.g. Mokoena"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 font-medium">Email *</label>
              <Input
                required
                type="email"
                value={form.email}
                onChange={e => handleChange('email', e.target.value)}
                placeholder="e.g. <EMAIL>"
              />
            </div>
            <div>
              <label className="block mb-1 font-medium">Cell Number *</label>
              <Input
                required
                value={form.cellNumber}
                onChange={e => handleChange('cellNumber', e.target.value)}
                placeholder="e.g. +266 6000 0000"
              />
            </div>
          </div>
          <div>
            <label className="block mb-1 font-medium">Other Cell Number</label>
            <Input
              value={form.otherCellNumber}
              onChange={e => handleChange('otherCellNumber', e.target.value)}
              placeholder="Optional"
            />
          </div>
          <div>
            <label className="block mb-1 font-medium">Referral Code *</label>
            <div className="flex gap-2 items-center">
              <Input
                required
                value={form.referralCode}
                onChange={e => {
                  handleChange('referralCode', e.target.value);
                  setAutoGenerated(false);
                }}
                disabled={autoGenerated}
                placeholder="e.g. LEBO01"
                className="w-40"
              />
              <Button type="button" variant={autoGenerated ? 'default' : 'outline'} onClick={handleAutoGenerate}>
                Auto-generate
              </Button>
              <Button type="button" variant={!autoGenerated ? 'default' : 'outline'} onClick={handleManual}>
                Manual
              </Button>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 font-medium">Discount Amount *</label>
              <Input
                required
                type="number"
                value={form.discountAmount}
                onChange={e => handleChange('discountAmount', e.target.value)}
                placeholder="e.g. 50"
                min="0"
                step="0.01"
              />
              <p className="text-sm text-gray-500 mt-1">Amount in Maloti (M)</p>
            </div>
            <div>
              <label className="block mb-1 font-medium">Discount Code *</label>
              <Input
                required
                value={form.discountCode}
                onChange={e => handleChange('discountCode', e.target.value)}
                placeholder="e.g. PARTNER50"
                className="uppercase"
              />
              <p className="text-sm text-gray-500 mt-1">Will be auto-applied when QR is scanned</p>
            </div>
          </div>
          {form.referralCode && form.discountCode && form.discountAmount && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h4 className="font-semibold text-blue-800 mb-4 text-center">QR Code Preview</h4>
              <div className="flex justify-center">
                <PartnerQRCode
                  referralCode={form.referralCode}
                  discountCode={form.discountCode}
                  discountAmount={parseFloat(form.discountAmount) || 0}
                  partnerName={`${form.name} ${form.surname}`}
                  size={200}
                  showDownload={true}
                  showRefresh={true}
                />
              </div>
              <div className="mt-4 text-center">
                <p className="text-sm text-blue-700">
                  When scanned, this QR code will automatically:
                </p>
                <ul className="text-sm text-blue-700 mt-2 list-disc list-inside text-left max-w-xs mx-auto">
                  <li>Fill in referral code: <code className="bg-blue-100 px-1 rounded">{form.referralCode}</code></li>
                  <li>Apply discount code: <code className="bg-blue-100 px-1 rounded">{form.discountCode}</code></li>
                  <li>Give discount of: <strong>M{form.discountAmount}</strong></li>
                </ul>
              </div>
            </div>
          )}
          <div className="pt-4">
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Creating Partner...' : 'Create Partner'}
            </Button>
          </div>
        </form>
      </div>
    </ProtectedRoute>
  );
} 