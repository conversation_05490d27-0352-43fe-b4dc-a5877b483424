"use client";
import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PartnerQRCode } from '@/components/ui/qr-code';
import AdminBackButton from "@/components/admin/common/admin-back-button";

export default function AdminPartnerDetailPage() {
  const router = useRouter();
  const params = useParams();
  const partnerId = params?.id as string;
  const [partner, setPartner] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [form, setForm] = useState<any>({});
  const [saving, setSaving] = useState(false);
  const [bonus, setBonus] = useState('');
  const [bonusLoading, setBonusLoading] = useState(false);

  useEffect(() => {
    if (!partnerId) return;
    setLoading(true);
    fetch(`/api/admin/partners/${partnerId}`)
      .then(res => res.json())
      .then(data => {
        setPartner(data.partner);
        setForm(data.partner);
        setBonus(data.partner.bonusPaid?.toString() || '');
      })
      .catch(() => setError('Failed to load partner'))
      .finally(() => setLoading(false));
  }, [partnerId]);

  const handleChange = (field: string, value: string) => {
    setForm((prev: any) => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setSaving(true);
    setError('');
    try {
      const res = await fetch(`/api/admin/partners/${partnerId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(form),
      });
      const result = await res.json();
      if (!res.ok) throw new Error(result.error || 'Failed to update partner');
      setPartner(result.partner);
      setEditMode(false);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleDeactivate = async () => {
    setSaving(true);
    setError('');
    try {
      const res = await fetch(`/api/admin/partners/${partnerId}/deactivate`, { method: 'POST' });
      const result = await res.json();
      if (!res.ok) throw new Error(result.error || 'Failed to deactivate');
      setPartner((prev: any) => ({ ...prev, isActive: false }));
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleReactivate = async () => {
    setSaving(true);
    setError('');
    try {
      const res = await fetch(`/api/admin/partners/${partnerId}/reactivate`, { method: 'POST' });
      const result = await res.json();
      if (!res.ok) throw new Error(result.error || 'Failed to reactivate');
      setPartner((prev: any) => ({ ...prev, isActive: true }));
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleBonus = async () => {
    setBonusLoading(true);
    setError('');
    try {
      const res = await fetch(`/api/admin/partners/${partnerId}/bonus`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ bonusPaid: parseFloat(bonus) }),
      });
      const result = await res.json();
      if (!res.ok) throw new Error(result.error || 'Failed to update bonus');
      setPartner((prev: any) => ({ ...prev, bonusPaid: result.bonusPaid }));
    } catch (err: any) {
      setError(err.message);
    } finally {
      setBonusLoading(false);
    }
  };

  if (loading) return <div className="p-8 text-center">Loading...</div>;
  if (error) return <div className="p-8 text-center text-red-600">{error}</div>;
  if (!partner) return <div className="p-8 text-center">Partner not found.</div>;

  return (
    <div className="max-w-3xl mx-auto py-8 px-2 md:px-0">
      <AdminBackButton href="/admin/partners" label="Back to Partners" />
      <Card>
        <CardHeader>
          <CardTitle>Partner Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {editMode ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block mb-1 font-medium">Name</label>
                <Input value={form.name} onChange={e => handleChange('name', e.target.value)} />
              </div>
              <div>
                <label className="block mb-1 font-medium">Surname</label>
                <Input value={form.surname} onChange={e => handleChange('surname', e.target.value)} />
              </div>
              <div>
                <label className="block mb-1 font-medium">Email</label>
                <Input value={form.email} onChange={e => handleChange('email', e.target.value)} />
              </div>
              <div>
                <label className="block mb-1 font-medium">Cell Number</label>
                <Input value={form.cellNumber} onChange={e => handleChange('cellNumber', e.target.value)} />
              </div>
              <div>
                <label className="block mb-1 font-medium">Other Cell Number</label>
                <Input value={form.otherCellNumber} onChange={e => handleChange('otherCellNumber', e.target.value)} />
              </div>
              <div>
                <label className="block mb-1 font-medium">Referral Code</label>
                <Input value={form.referralCode} onChange={e => handleChange('referralCode', e.target.value)} />
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="font-medium">{partner.name} {partner.surname}</div>
                <div className="text-gray-500 text-sm">{partner.email}</div>
                <div className="text-gray-500 text-sm">{partner.cellNumber}</div>
                {partner.otherCellNumber && <div className="text-gray-500 text-sm">Other: {partner.otherCellNumber}</div>}
                <div className="mt-2">
                  <span className="font-semibold">Referral Code:</span> <code className="bg-gray-100 px-2 py-1 rounded text-sm">{partner.referralCode}</code>
                </div>
                <div className="mt-2">
                  <span className="font-semibold">Status:</span> {partner.isActive ? <span className="text-green-600">Active</span> : <span className="text-red-600">Inactive</span>}
                </div>
              </div>
              <div>
                <div className="mb-2">
                  <span className="font-semibold">Commission Earned:</span> M{partner.commissionEarned?.toFixed(2) || '0.00'}
                </div>
                <div className="mb-2">
                  <span className="font-semibold">Bonus Paid:</span> M{partner.bonusPaid?.toFixed(2) || '0.00'}
                </div>
                <div className="mb-2">
                  <span className="font-semibold">Joined:</span> {new Date(partner.createdAt).toLocaleDateString()}
                </div>
                <div className="mb-2">
                  <span className="font-semibold">Total Referrals:</span> {partner.referralOrders?.length || 0}
                </div>
              </div>
            </div>
          )}

          {/* QR Code Section */}
          {!editMode && partner.discountCode && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h4 className="font-semibold text-blue-800 mb-4 text-center">Partner QR Code</h4>
              <div className="flex justify-center">
                <PartnerQRCode
                  referralCode={partner.referralCode}
                  discountCode={partner.discountCode}
                  discountAmount={partner.discountAmount || 0}
                  partnerName={`${partner.name} ${partner.surname}`}
                  size={200}
                  showDownload={true}
                  showRefresh={true}
                />
              </div>
            </div>
          )}

          <div className="flex gap-2 mt-4">
            {editMode ? (
              <>
                <Button onClick={handleSave} disabled={saving}>{saving ? 'Saving...' : 'Save'}</Button>
                <Button variant="outline" onClick={() => setEditMode(false)} disabled={saving}>Cancel</Button>
              </>
            ) : (
              <Button onClick={() => setEditMode(true)}>Edit</Button>
            )}
            {partner.isActive ? (
              <Button variant="destructive" onClick={handleDeactivate} disabled={saving}>Deactivate</Button>
            ) : (
              <Button variant="outline" onClick={handleReactivate} disabled={saving}>Reactivate</Button>
            )}
          </div>

          <div className="mt-6">
            <label className="block mb-1 font-medium">Adjust Bonus Paid (M)</label>
            <div className="flex gap-2 items-center">
              <Input value={bonus} onChange={e => setBonus(e.target.value)} type="number" min="0" step="0.01" className="w-32" />
              <Button onClick={handleBonus} disabled={bonusLoading}>{bonusLoading ? 'Saving...' : 'Update Bonus'}</Button>
            </div>
          </div>

          <div className="mt-8">
            <h3 className="font-semibold mb-2">Referred Orders</h3>
            {partner.referralOrders && partner.referralOrders.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2 px-2">Customer</th>
                      <th className="text-left py-2 px-2">Order Value</th>
                      <th className="text-left py-2 px-2">Commission</th>
                      <th className="text-left py-2 px-2">Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {partner.referralOrders.map((order: any) => (
                      <tr key={order.id} className="border-b">
                        <td className="py-2 px-2">{order.customerName}</td>
                        <td className="py-2 px-2">M{order.orderValue.toFixed(2)}</td>
                        <td className="py-2 px-2">M{order.commission.toFixed(2)}</td>
                        <td className="py-2 px-2">{new Date(order.createdAt).toLocaleDateString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-gray-500">No referred orders yet.</div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 