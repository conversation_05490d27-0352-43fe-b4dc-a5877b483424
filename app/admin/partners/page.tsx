'use client';

import { ProtectedRoute } from '@/components/auth/protected-route';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';

interface Partner {
  id: string;
  name: string;
  surname: string;
  email: string;
  cellNumber: string;
  referralCode: string;
  isActive: boolean;
  commissionEarned: number;
  bonusPaid: number;
  createdAt: string;
  _count: {
    referralOrders: number;
  };
}

function AdminPartnersPageInner() {
  const [partners, setPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });
  const [error, setError] = useState('');
  const searchParams = useSearchParams();
  const message = searchParams.get('message');

  const fetchPartners = async (page = 1, searchTerm = '') => {
    try {
      setLoading(true);
      setError('');
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...(searchTerm && { search: searchTerm })
      });

      const response = await fetch(`/api/admin/partners?${params}`);
      const data = await response.json();

      if (response.ok) {
        setPartners(data.partners);
        setPagination(data.pagination);
      } else {
        setError(data.error || 'Failed to fetch partners');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch partners');
      console.error('Error fetching partners:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPartners();
  }, []);

  const handleSearch = (value: string) => {
    setSearch(value);
    fetchPartners(1, value);
  };

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'LSL'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <ProtectedRoute requiredRole="ADMIN">
      <div className="container mx-auto py-8 px-2 md:px-8">
        {message === 'partner_created' && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
            Partner created successfully!
          </div>
        )}
        {message === 'partner_updated' && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
            Partner updated successfully!
          </div>
        )}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
          <h1 className="text-2xl font-bold">Sales Partners</h1>
          <Link href="/admin/partners/new">
            <Button>Add New Partner</Button>
          </Link>
        </div>
        <div className="mb-4 flex flex-col md:flex-row gap-2 md:gap-4">
          <Input
            type="text"
            placeholder="Search by name, email, or code..."
            className="w-full md:w-1/3"
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
          <Button variant="outline" disabled>Export CSV</Button>
        </div>
        <div className="bg-white rounded shadow p-4 overflow-x-auto">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          {loading ? (
            <div className="text-center text-gray-500 py-12">
              Loading partners...
            </div>
          ) : partners.length === 0 ? (
            <div className="text-center text-gray-500 py-12">
              No partners found. Click "Add New Partner" to create one.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-2">Name</th>
                    <th className="text-left py-3 px-2">Email</th>
                    <th className="text-left py-3 px-2">Referral Code</th>
                    <th className="text-left py-3 px-2">Orders</th>
                    <th className="text-left py-3 px-2">Commission</th>
                    <th className="text-left py-3 px-2">Status</th>
                    <th className="text-left py-3 px-2">Created</th>
                    <th className="text-left py-3 px-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {partners.map((partner) => (
                    <tr key={partner.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-2">
                        <div>
                          <div className="font-medium">{partner.name} {partner.surname}</div>
                          <div className="text-sm text-gray-500">{partner.cellNumber}</div>
                        </div>
                      </td>
                      <td className="py-3 px-2">{partner.email}</td>
                      <td className="py-3 px-2">
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {partner.referralCode}
                        </code>
                      </td>
                      <td className="py-3 px-2">{partner._count.referralOrders}</td>
                      <td className="py-3 px-2">{formatPrice(partner.commissionEarned)}</td>
                      <td className="py-3 px-2">
                        <span className={`px-2 py-1 rounded text-xs ${
                          partner.isActive 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {partner.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="py-3 px-2 text-sm text-gray-500">
                        {formatDate(partner.createdAt)}
                      </td>
                      <td className="py-3 px-2">
                        <div className="flex gap-2">
                          <Link href={`/admin/partners/${partner.id}/edit`}>
                            <Button size="sm" variant="outline">Edit</Button>
                          </Link>
                          <Link href={`/admin/partners/${partner.id}`}>
                            <Button size="sm" variant="outline">View</Button>
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}

export default function AdminPartnersPage() {
  return (
    <Suspense>
      <AdminPartnersPageInner />
    </Suspense>
  );
} 