"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import AdminLayout from "@/components/admin/admin-layout";
import AdminNoticesContent from "@/components/admin/notices/admin-notices-content";
import { authClient } from "@/lib/auth-client";
import { UserRole } from "@/utils/types";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

export default function AdminNoticesPage() {
  const router = useRouter();
  const { data, isPending } = authClient.useSession();

  useEffect(() => {
    if (!isPending && !data?.user) {
      router.replace("/sign-in");
    }
  }, [isPending, data, router]);

  if (isPending) {
    return <div className="flex justify-center items-center h-full w-full"><SpinnerCircle4/></div>;
  }

  if (!data?.user) {
    return <div className="flex w-full h-full justify-center items-center"><SpinnerCircle4/></div>;
  }

  return (
    <AdminLayout
      user={{
        id: data.user.id,
        name: data.user.name,
        email: data.user.email,
        emailVerified: data.user.emailVerified,
        createdAt: data.user.createdAt,
        updatedAt: data.user.updatedAt,
        image: data.user.image,
        role: (data.user as any)["role"] ?? UserRole.ADMIN,
      }}
    >
      <div className="max-w-6xl mx-auto py-8 px-4">
        <AdminNoticesContent />
      </div>
    </AdminLayout>
  );
} 