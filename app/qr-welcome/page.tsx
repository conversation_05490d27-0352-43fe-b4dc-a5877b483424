"use client";

import Image from "next/image";
import Link from "next/link";

export default function QRWelcomePage() {
  return (
    <main className="min-h-screen flex flex-col items-center justify-between bg-black text-white px-4 py-8 relative">
      {/* Logo */}
      <div className="w-full flex justify-center mt-4 mb-8">
        <Image
          src="/logo.jpeg"
          alt="Rivv Sneakers Logo"
          width={120}
          height={120}
          className="rounded-full shadow-lg border-4 border-[#1b1f3b] bg-white object-cover"
          priority
        />
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md mx-auto">
        <h1 className="text-3xl sm:text-4xl font-extrabold text-center mb-4 tracking-tight">
          <span role="img" aria-label="party">🎉</span> Welcome to <span className="text-[#1b1f3b]">Rivv Sneakers</span>
        </h1>
        <p className="text-lg sm:text-xl text-center mb-6 text-gray-200">
          You’re now part of the freshest sneaker fam in Lesotho.<br/>
          We’ve got <span className="font-semibold text-white">Nike, Adidas, Timberland, New Balance & more</span> — all at <span className="text-[#1b1f3b] font-bold">🔥 prices (M900–M2050)</span>.
        </p>
        <ul className="text-base sm:text-lg text-left mb-8 w-full max-w-xs mx-auto space-y-2">
          <li className="flex items-center gap-2"><span className="text-green-400">✅</span> Free delivery in Maseru</li>
          <li className="flex items-center gap-2"><span className="text-green-400">✅</span> Lay-Buy: 60% deposit, 6 weeks</li>
          <li className="flex items-center gap-2"><span className="text-green-400">✅</span> <span className="font-semibold">Exclusive Coupon:</span> <span className="bg-[#1b1f3b] text-white px-2 py-1 rounded ml-1">MM350</span> <span className="text-gray-300">(worth M350 off)</span></li>
        </ul>
        <Link href="/products" className="w-full">
          <button className="w-full py-4 rounded-full bg-[#1b1f3b] text-white text-xl font-bold shadow-lg hover:bg-white hover:text-[#1b1f3b] transition mb-4">
            Shop
          </button>
        </Link>
        <div className="w-full text-center mt-2">
          <Link href="/sign-in" className="text-sm text-gray-300 underline hover:text-white">
            Already have an account? Log in here
          </Link>
        </div>
      </div>

      {/* Footer (optional) */}
      <footer className="w-full text-center text-xs text-gray-500 mt-8 opacity-70">
        &copy; {new Date().getFullYear()} Rivv Sneakers. All rights reserved.
      </footer>
    </main>
  );
} 