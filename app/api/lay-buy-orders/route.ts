import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";
import { calculateDeliveryFee as calculateDistrictBasedDeliveryFee } from "@/lib/product-utils";
import {
  sendLayBuyOrderConfirmation,
  sendAdminOrderNotification,
  sendPartnerReferralNotification
} from "@/lib/email-service";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// POST /api/lay-buy-orders - Create new Lay-Buy order
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      shippingAddress,
      phoneNumber,
      notes,
      discountAmount = 0,
      discountCode,
      paymentMethod,
      paymentProofUrl,
      deliveryFee: deliveryFeeParam = 0,
      items,
      layBuy,
    } = body;

    // Validate required fields
    if (!shippingAddress || !phoneNumber || !items || items.length === 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    if (!layBuy || !layBuy.totalAmount || !layBuy.upfrontAmount || !layBuy.remainingAmount) {
      return NextResponse.json(
        { success: false, error: "Missing Lay-Buy information" },
        { status: 400 }
      );
    }

    // Validate items exist and have sufficient stock
    const productIds = items.map((item: any) => item.productId);
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } },
    });

    if (products.length !== productIds.length) {
      return NextResponse.json(
        { success: false, error: "Some products not found" },
        { status: 400 }
      );
    }

    // Check stock availability
    for (const item of items) {
      const product = products.find(p => p.id === item.productId);
      if (!product || product.stock < item.quantity) {
        return NextResponse.json(
          { success: false, error: `Insufficient stock for ${product?.name || 'product'}` },
          { status: 400 }
        );
      }
    }

    // Calculate total shoes for delivery fee
    let totalShoes = 0;
    const orderItemsData = items.map((item: any) => {
      totalShoes += item.quantity;
      return {
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
        size: item.size || "",
        color: item.color || "",
      };
    });

    // Use provided delivery fee from frontend (calculated based on district)
    // If not provided, fall back to district-based calculation
    let deliveryFee = deliveryFeeParam || 0;
    let isBulkDelivery = false;

    if (deliveryFee === undefined || deliveryFee === null || deliveryFee === 0) {
      // Extract district from shipping address for fallback calculation
      const addressParts = shippingAddress.split(', ');
      const district = addressParts.length >= 3 ? addressParts[2] : 'Maseru';
      // Calculate subtotal for delivery fee calculation
      const subtotal = items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
      const subtotalAfterDiscount = subtotal - discountAmount;
      const deliveryInfo = calculateDistrictBasedDeliveryFee(district, subtotalAfterDiscount);
      deliveryFee = deliveryInfo.fee;
    }

    // Validate discount code and check for partner referral if provided
    let discountCodeId = null;
    let partner = null;
    if (discountCode) {
      // First, try to find a regular discount code
      const discount = await prisma.discountCode.findUnique({
        where: { code: discountCode, isActive: true },
      });

      if (discount) {
        // Check if discount code is not yet valid
        if (discount.validFrom && new Date() < discount.validFrom) {
          return NextResponse.json(
            { success: false, error: "Discount code is not yet active" },
            { status: 400 }
          );
        }

        // Check if discount is still valid
        if (discount.validUntil && discount.validUntil < new Date()) {
          return NextResponse.json(
            { success: false, error: "Discount code has expired" },
            { status: 400 }
          );
        }

        // Check usage limits
        if (discount.maxUses && discount.usedCount >= discount.maxUses) {
          return NextResponse.json(
            { success: false, error: "Discount code usage limit reached" },
            { status: 400 }
          );
        }

        discountCodeId = discount.id;
      } else {
        // If not found as discount code, check if it's a partner referral code
        partner = await prisma.salesPartner.findUnique({
          where: { referralCode: discountCode, isActive: true },
        });

        if (!partner) {
          return NextResponse.json(
            { success: false, error: "Invalid discount code" },
            { status: 400 }
          );
        }
      }
    }

    // Create Lay-Buy order in transaction with increased timeout
    const result = await prisma.$transaction(async (tx) => {
      // Update product stock
      const stockUpdates = items.map((item: any) =>
        tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        })
      );
      await Promise.all(stockUpdates);

      // Clear user's cart
      await tx.cartItem.deleteMany({
        where: { userId: user.id },
      });

      // Update discount code usage if applicable
      if (discountCodeId) {
        await tx.discountCode.update({
          where: { id: discountCodeId },
          data: {
            usedCount: { increment: 1 },
          },
        });
      }

      // Create the Lay-Buy order directly in the transaction
      const orderNumber = `LB-${Date.now().toString(36).toUpperCase()}-${Math.random().toString(36).substring(2, 6).toUpperCase()}`;
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + (6 * 7)); // 6 weeks
      const gracePeriodEnd = new Date(dueDate);
      gracePeriodEnd.setDate(gracePeriodEnd.getDate() + 7); // 1 week grace period

      const layBuyOrder = await tx.layBuyOrder.create({
        data: {
          userId: user.id,
          orderNumber,
          status: "ACTIVE",
          totalAmount: layBuy.totalAmount,
          upfrontAmount: layBuy.upfrontAmount,
          remainingAmount: layBuy.remainingAmount,
          amountPaid: layBuy.upfrontAmount,
          dueDate,
          gracePeriodEnd,
          shippingAddress,
          phoneNumber,
          notes,
          orderItems: {
            create: orderItemsData,
          },
        },
        include: {
          orderItems: {
            include: {
              product: true,
            },
          },
        },
      });

      // Add the upfront payment record if payment proof is provided
      if (paymentProofUrl) {
        await tx.layBuyPayment.create({
          data: {
            layBuyOrderId: layBuyOrder.id,
            amount: layBuy.upfrontAmount,
            paymentType: "UPFRONT",
            paymentMethod,
            paymentProof: paymentProofUrl,
            notes: `Upfront payment for Lay-Buy order ${layBuyOrder.orderNumber}`,
            status: "PENDING", // Will be verified by admin
          },
        });
      }

      return layBuyOrder;
    }, {
      timeout: 15000, // Increase timeout to 15 seconds
    });

    // --- Referral System Logic ---
    // Note: For now, we'll track partner referrals for lay-buy orders differently
    // since the current ReferralOrder model is designed for regular orders only.
    // The partner will still receive email notifications about the referral.
    if (discountCode && partner) {
      // Calculate commission: 5% of order value before discount and delivery
      const orderValue = items.reduce((sum: any, item: any) => sum + (item.price * item.quantity), 0);
      const commission = Math.round(orderValue * 0.05 * 100) / 100;

      // Update partner's commission earned (we'll track this even without the ReferralOrder record for now)
      await prisma.salesPartner.update({
        where: { id: partner.id },
        data: {
          commissionEarned: { increment: commission },
        }
      });

      console.log(`[LAY-BUY] Partner ${partner.name} earned commission of M${commission} for lay-buy order ${result.orderNumber}`);
    }
    // --- End Referral System Logic ---

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
    };

    // Send emails asynchronously (don't block the response)
    try {
      // Refetch the order with user data for email
      const orderWithUser = await prisma.layBuyOrder.findUnique({
        where: { id: result.id },
        include: {
          user: true,
          orderItems: {
            include: {
              product: true,
            },
          },
        },
      });

      if (orderWithUser) {
        const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
        const customerEmail = orderWithUser.user?.email;
        const emailPromises = [];

        // Customer confirmation email
        if (customerEmail) {
          emailPromises.push(
            sendLayBuyOrderConfirmation(orderWithUser).catch(error => {
              console.error(`[EMAIL] Failed to send lay-buy order confirmation to customer (${customerEmail}):`, error);
            })
          );
        }

        // Admin notification (convert lay-buy order to regular order format for email template)
        const orderForAdmin = {
          id: orderWithUser.id,
          orderNumber: orderWithUser.orderNumber,
          totalAmount: orderWithUser.totalAmount,
          shippingAddress: orderWithUser.shippingAddress,
          phoneNumber: orderWithUser.phoneNumber,
          notes: orderWithUser.notes,
          createdAt: orderWithUser.createdAt,
          user: orderWithUser.user,
          orderItems: orderWithUser.orderItems.map((item: any) => ({
            product: item.product,
            quantity: item.quantity,
            price: item.price,
            size: item.size,
          })),
        };

        emailPromises.push(
          sendAdminOrderNotification(orderForAdmin as any, paymentMethod).catch(error => {
            console.error(`[EMAIL] Failed to send admin lay-buy order notification to admin (${adminEmail}):`, error);
          })
        );

        // Partner notification (if applicable) - Note: Delva is intentionally excluded for lay-buy orders
        if (partner) {
          emailPromises.push(
            sendPartnerReferralNotification(orderForAdmin as any, partner).catch(error => {
              console.error(`[EMAIL] Failed to send partner referral notification to partner (${partner.email || partner.id}):`, error);
            })
          );
        }

        // Wait for all emails to complete
        await Promise.allSettled(emailPromises);
      }
    } catch (emailError) {
      console.error("Failed to send Lay-Buy order emails:", emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error creating Lay-Buy order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create Lay-Buy order" },
      { status: 500 }
    );
  }
}

// GET /api/lay-buy-orders - Get user's Lay-Buy orders
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const orders = await prisma.layBuyOrder.findMany({
      where: { userId: user.id },
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        payments: {
          orderBy: { createdAt: "desc" },
        },
        _count: {
          select: {
            payments: true,
            reminders: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    const response: ApiResponse<typeof orders> = {
      success: true,
      data: orders,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching Lay-Buy orders:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch Lay-Buy orders" },
      { status: 500 }
    );
  }
}
