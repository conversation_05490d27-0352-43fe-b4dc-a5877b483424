import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { addLayBuyPayment, getLayBuyPaymentHistory } from "@/actions/layBuyPaymentActions";
import { getLayBuyOrderById } from "@/actions/layBuyActions";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// POST /api/lay-buy-orders/[id]/payments - Add a new payment
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: paramsId } = await context.params;
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Verify the order belongs to the user
    const orderResult = await getLayBuyOrderById(paramsId);
    if (!orderResult.success || !orderResult.data) {
      return NextResponse.json(
        { success: false, error: "Order not found" },
        { status: 404 }
      );
    }

    const order = orderResult.data;
    if (order.userId !== user.id && user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Access denied" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { amount, paymentMethod, paymentProof, notes } = body;

    // Validate required fields
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { success: false, error: "Valid payment amount is required" },
        { status: 400 }
      );
    }

    // Determine payment type based on current order state
    let paymentType: "UPFRONT" | "INSTALLMENT" | "COMPLETION";
    const remainingBalance = order.totalAmount - (order.amountPaid ?? 0);

    if (order.amountPaid === 0) {
      paymentType = "UPFRONT";
    } else if (amount >= remainingBalance) {
      paymentType = "COMPLETION";
    } else {
      paymentType = "INSTALLMENT";
    }

    const result = await addLayBuyPayment({
      layBuyOrderId: paramsId,
      amount: parseFloat(amount),
      paymentType,
      paymentMethod,
      paymentProof,
      notes,
      userId: user.id,
    });

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    const response: ApiResponse<typeof result.data> = {
      success: true,
      data: result.data,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error adding payment:", error);
    return NextResponse.json(
      { success: false, error: "Failed to add payment" },
      { status: 500 }
    );
  }
}

// GET /api/lay-buy-orders/[id]/payments - Get payment history for an order
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: paramsId } = await context.params;
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Verify the order belongs to the user
    const orderResult = await getLayBuyOrderById(paramsId);
    if (!orderResult.success || !orderResult.data) {
      return NextResponse.json(
        { success: false, error: "Order not found" },
        { status: 404 }
      );
    }

    const order = orderResult.data;
    if (order.userId !== user.id && user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Access denied" },
        { status: 403 }
      );
    }

    const result = await getLayBuyPaymentHistory(paramsId);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    const response: ApiResponse<typeof result.data> = {
      success: true,
      data: result.data,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching payment history:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch payment history" },
      { status: 500 }
    );
  }
}
