import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { getLayBuyOrderById } from "@/actions/layBuyActions";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// GET /api/lay-buy-orders/[id] - Get single Lay-Buy order
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: paramsId } = await context.params;
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const result = await getLayBuyOrderById(paramsId);
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 404 }
      );
    }

    const order = result.data;
    
    // Ensure user can only access their own orders (unless admin)
    if (order?.userId !== user.id && user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Access denied" },
        { status: 403 }
      );
    }

    const response: ApiResponse<typeof order> = {
      success: true,
      data: order,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching Lay-Buy order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch Lay-Buy order" },
      { status: 500 }
    );
  }
}
