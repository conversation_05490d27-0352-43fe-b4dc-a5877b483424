import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";
import { calculateRefundAmount } from "@/lib/lay-buy-utils";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// POST /api/lay-buy-orders/[id]/cancel-request - Submit cancellation request
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: orderId } = await context.params;
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { reason } = body;

    // Get the order and verify ownership
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: orderId },
      include: {
        payments: {
          where: { status: "VERIFIED" },
        },
      },
    });

    if (!order) {
      return NextResponse.json(
        { success: false, error: "Order not found" },
        { status: 404 }
      );
    }

    if (order.userId !== user.id) {
      return NextResponse.json(
        { success: false, error: "Access denied" },
        { status: 403 }
      );
    }

    if (order.status !== "ACTIVE") {
      return NextResponse.json(
        { success: false, error: "Can only request cancellation for active orders" },
        { status: 400 }
      );
    }

    // Check if there's already a pending cancellation request
    const existingRequest = await prisma.layBuyCancellationRequest.findFirst({
      where: {
        layBuyOrderId: orderId,
        status: "PENDING",
      },
    });

    if (existingRequest) {
      return NextResponse.json(
        { success: false, error: "A cancellation request is already pending for this order" },
        { status: 400 }
      );
    }

    // Calculate refund amount
    const totalPaid = order.payments.reduce((sum, payment) => sum + (payment.amount ?? 0), 0);
    const refundInfo = calculateRefundAmount(totalPaid, order.dueDate ?? new Date());

    // Create cancellation request
    const cancellationRequest = await prisma.layBuyCancellationRequest.create({
      data: {
        layBuyOrderId: orderId,
        requestedById: user.id,
        reason: reason || "No reason provided",
        refundAmount: refundInfo.isEligible ? refundInfo.refundAmount : 0,
      },
      include: {
        layBuyOrder: {
          select: {
            orderNumber: true,
            totalAmount: true,
            amountPaid: true,
          },
        },
        requestedBy: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    const response: ApiResponse<typeof cancellationRequest> = {
      success: true,
      data: cancellationRequest,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error creating cancellation request:", error);
    return NextResponse.json(
      { success: false, error: "Failed to submit cancellation request" },
      { status: 500 }
    );
  }
}

// GET /api/lay-buy-orders/[id]/cancel-request - Get cancellation request status
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: orderId } = await context.params;
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the order and verify ownership
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: orderId },
      select: { userId: true },
    });

    if (!order) {
      return NextResponse.json(
        { success: false, error: "Order not found" },
        { status: 404 }
      );
    }

    if (order.userId !== user.id) {
      return NextResponse.json(
        { success: false, error: "Access denied" },
        { status: 403 }
      );
    }

    // Get cancellation request
    const cancellationRequest = await prisma.layBuyCancellationRequest.findFirst({
      where: {
        layBuyOrderId: orderId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const response: ApiResponse<typeof cancellationRequest> = {
      success: true,
      data: cancellationRequest,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching cancellation request:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch cancellation request" },
      { status: 500 }
    );
  }
}
