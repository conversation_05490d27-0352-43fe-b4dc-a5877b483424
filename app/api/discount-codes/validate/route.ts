import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";

// POST /api/discount-codes/validate - Validate discount code
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { code, orderAmount } = body;

    if (!code || !orderAmount) {
      return NextResponse.json(
        { success: false, error: "Code and order amount are required" },
        { status: 400 }
      );
    }

    // First, try to find a discount code
    let discountCode = await prisma.discountCode.findUnique({
      where: { code: code.toUpperCase(), isActive: true },
    });

    // If not found as discount code, check if it's a referral code
    if (!discountCode) {
      const partner = await prisma.salesPartner.findUnique({
        where: { referralCode: code.toUpperCase(), isActive: true },
      });

      if (partner) {
        // Create a virtual discount code from partner data
        discountCode = {
          id: `partner-${partner.id}`,
          code: partner.referralCode,
          description: `Partner referral discount`,
          type: 'FIXED_AMOUNT' as any,
          value: partner.discountAmount,
          minAmount: null,
          maxUses: null,
          usedCount: 0,
          isActive: true,
          validFrom: new Date(),
          validUntil: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      }
    }

    if (!discountCode) {
      return NextResponse.json(
        { success: false, error: "Invalid discount or referral code" },
        { status: 400 }
      );
    }

    // Check if discount code is still valid
    const now = new Date();
    if (discountCode.validFrom > now) {
      return NextResponse.json(
        { success: false, error: "Discount code is not yet active" },
        { status: 400 }
      );
    }

    if (discountCode.validUntil && discountCode.validUntil < now) {
      return NextResponse.json(
        { success: false, error: "Discount code has expired" },
        { status: 400 }
      );
    }

    // Check usage limits
    if (discountCode.maxUses && discountCode.usedCount >= discountCode.maxUses) {
      return NextResponse.json(
        { success: false, error: "Discount code usage limit reached" },
        { status: 400 }
      );
    }

    // Check minimum order amount
    if (discountCode.minAmount && orderAmount < discountCode.minAmount) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Minimum order amount of M ${discountCode.minAmount} required` 
        },
        { status: 400 }
      );
    }

    const response: ApiResponse<typeof discountCode> = {
      success: true,
      data: discountCode,
      message: "Discount code is valid",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error validating discount code:", error);
    return NextResponse.json(
      { success: false, error: "Failed to validate discount code" },
      { status: 500 }
    );
  }
}
