import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || !user.isPartner) {
      return NextResponse.json({ error: 'You are not authorized to view this dashboard. Please sign in as a partner.' }, { status: 403 });
    }
    const partner = await prisma.salesPartner.findUnique({
      where: { email: user.email },
      include: {
        referralOrders: { orderBy: { createdAt: 'desc' } },
      },
    });
    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
    }
    return NextResponse.json({ partner });
  } catch (error) {
    console.error('Error fetching partner dashboard:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Failed to fetch dashboard. Please try again or contact support.' }, { status: 500 });
  }
} 