import { NextRequest, NextResponse } from "next/server";
import { ApiResponse, OrderStatus } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import { sendOrderStatusUpdateEmail } from "@/lib/email-service";
import prisma from "@/lib/prisma";
import { checkAndGenerateLoyaltyDiscount, sendLoyaltyDiscountNotification } from "@/lib/loyalty-system";


// PUT /api/orders/[id]/status - Update order status (admin only)
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const {id: paramsId} = await context.params;

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { status, trackingInfo, cancellationReason } = body;

    // Validate status
    const validStatuses: OrderStatus[] = ["PENDING", "CONFIRMED", "PROCESSING", "SHIPPED", "DELIVERED", "CANCELLED"];
    if (!status || !validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: "Invalid order status" },
        { status: 400 }
      );
    }

    // Find the order
    const existingOrder = await prisma.order.findUnique({
      where: { id: paramsId },
      include: {
        user: true,
        orderItems: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!existingOrder) {
      return NextResponse.json(
        { success: false, error: "Order not found" },
        { status: 404 }
      );
    }

    // Update order status
    const updatedOrder = await prisma.order.update({
      where: { id: paramsId },
      data: { status },
      include: {
        user: true,
        orderItems: {
          include: {
            product: {
              include: {
                category: true,
              },
            },
          },
        },
        discountCode: true,
        paymentProof: true,
      },
    });

    // Send status update email if status changed and it's a customer-facing status
    const emailStatuses: OrderStatus[] = ["CONFIRMED", "PROCESSING", "SHIPPED", "DELIVERED", "CANCELLED"];
    if (existingOrder.status !== status && emailStatuses.includes(status)) {
      // Send email asynchronously to avoid blocking the response
      sendOrderStatusUpdateEmail(updatedOrder, status, trackingInfo, cancellationReason).catch(error => {
        console.error("Failed to send order status update email:", error);
        // Log the error but don't fail the request
      });
    }

    // Check for loyalty discount eligibility when order is delivered
    if (status === 'DELIVERED' && existingOrder.status !== 'DELIVERED') {
      checkAndGenerateLoyaltyDiscount(updatedOrder.userId).then(async (loyaltyResult) => {
        if (loyaltyResult.discountGenerated && loyaltyResult.discountCode) {
          console.log(`🎉 Loyalty discount generated for order ${updatedOrder.orderNumber}: ${loyaltyResult.discountCode}`);
          // Send loyalty discount notification email
          await sendLoyaltyDiscountNotification(updatedOrder.userId, loyaltyResult.discountCode);
        }
      }).catch(loyaltyError => {
        console.error("Failed to process loyalty discount:", loyaltyError);
        // Don't fail the request if loyalty processing fails
      });
    }

    const response: ApiResponse<typeof updatedOrder> = {
      success: true,
      data: updatedOrder,
      message: "Order status updated successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating order status:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update order status" },
      { status: 500 }
    );
  }
}
