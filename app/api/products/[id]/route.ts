import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";

// GET /api/products/[id] - Get single product with details
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: paramsId } = await context.params;
    const product = await prisma.product.findUnique({
      where: {
        id: paramsId,
        isActive: true,
      },
      include: {
        category: true,
        reviews: {
          orderBy: { createdAt: "desc" },
          take: 10, // Limit to recent 10 reviews
        },
        _count: {
          select: { reviews: true },
        },
      },
    });

    if (!product) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    const response: ApiResponse<typeof product> = {
      success: true,
      data: product,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch product" },
      { status: 500 }
    );
  }
}
