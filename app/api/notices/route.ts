import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";

// GET /api/notices - fetch all notices for the current user, with read/unread status
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    // Fetch all active notices
    const notices = await prisma.notice.findMany({
      where: { isActive: true },
      orderBy: { createdAt: "desc" },
    });

    // Fetch which notices this user has read
    const readNotices = await prisma.noticeRead.findMany({
      where: { userId: user.id },
      select: { noticeId: true },
    });
    const readIds = new Set(readNotices.map(n => n.noticeId));

    // Attach isRead to each notice
    const result = notices.map(n => ({
      ...n,
      isRead: readIds.has(n.id),
    }));

    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error("Error in GET /api/notices:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PATCH /api/notices - mark a notice as read for the user
export async function PATCH(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ success: false, error: "Not authenticated" }, { status: 401 });
    }
    const { noticeId } = await req.json();
    if (!noticeId) {
      return NextResponse.json({ success: false, error: "Missing noticeId" }, { status: 400 });
    }
    // Upsert NoticeRead
    await prisma.noticeRead.upsert({
      where: { userId_noticeId: { userId: user.id, noticeId } },
      update: { readAt: new Date() },
      create: { userId: user.id, noticeId },
    });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error in PATCH /api/notices:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
} 