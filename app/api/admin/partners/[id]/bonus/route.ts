import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth-utils';

export async function POST(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'You are not authorized to update bonus. Please sign in as an admin.' }, { status: 401 });
    }
    const body = await request.json();
    const { bonusPaid } = body;
    if (typeof bonusPaid !== 'number' || isNaN(bonusPaid) || bonusPaid < 0) {
      return NextResponse.json({ error: 'Invalid bonus amount' }, { status: 400 });
    }
    const { id: paramsId } = await context.params;
    const partner = await prisma.salesPartner.update({
      where: { id: paramsId },
      data: { bonusPaid },
    });
    return NextResponse.json({ success: true, bonusPaid: partner.bonusPaid });
  } catch (error) {
    console.error('Error updating bonus:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Failed to update bonus. Please try again or contact support.' }, { status: 500 });
  }
} 