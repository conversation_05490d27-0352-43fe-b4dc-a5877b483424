import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth-utils';

export async function POST(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'You are not authorized to deactivate this partner. Please sign in as an admin.' }, { status: 401 });
    }
    const { id: paramsId } = await context.params;
    await prisma.salesPartner.update({
      where: { id: paramsId },
      data: { isActive: false },
    });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deactivating partner:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Failed to deactivate partner. Please try again or contact support.' }, { status: 500 });
  }
} 