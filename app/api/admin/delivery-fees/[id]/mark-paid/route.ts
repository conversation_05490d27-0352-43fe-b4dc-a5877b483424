import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { ApiResponse } from "@/utils/types";

// POST /api/admin/delivery-fees/[id]/mark-paid - Mark delivery fee as paid
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const { id: paramsId } = await context.params;

    // Check if order exists and has a delivery fee
    const order = await prisma.order.findUnique({
      where: { id: paramsId },
    });

    if (!order) {
      return NextResponse.json(
        { success: false, error: "Order not found" },
        { status: 404 }
      );
    }

    if (!order.deliveryFee) {
      return NextResponse.json(
        { success: false, error: "Order has no delivery fee" },
        { status: 400 }
      );
    }

    if (order.deliveryFeePaid) {
      return NextResponse.json(
        { success: false, error: "Delivery fee already marked as paid" },
        { status: 400 }
      );
    }

    // Mark delivery fee as paid
    const updatedOrder = await prisma.order.update({
      where: { id: paramsId },
      data: {
        deliveryFeePaid: true,
        deliveryFeePaidAt: new Date(),
      },
    });

    const response: ApiResponse<typeof updatedOrder> = {
      success: true,
      data: updatedOrder,
      message: "Delivery fee marked as paid successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error marking delivery fee as paid:", error);
    return NextResponse.json(
      { success: false, error: "Failed to mark delivery fee as paid" },
      { status: 500 }
    );
  }
}
