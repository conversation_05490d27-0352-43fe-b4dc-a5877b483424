import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";

// GET /api/admin/delivery-fees/export - Export delivery fees report as CSV
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    // Get all delivered orders with delivery fees
    const deliveredOrders = await prisma.order.findMany({
      where: {
        status: "DELIVERED",
        deliveryFee: { not: null },
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        orderItems: {
          select: {
            quantity: true,
            product: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        deliveredAt: 'desc',
      },
    });

    // Create CSV content
    const csvHeaders = [
      'Order Number',
      'Customer Name',
      'Customer Email',
      'Delivered Date',
      'Shoes Count',
      'Delivery Type',
      'Fee Per Shoe',
      'Total Delivery Fee',
      'Payment Status',
      'Paid Date',
      'Products'
    ];

    const csvRows = deliveredOrders.map(order => {
      const shoesCount = order.orderItems.reduce((sum, item) => sum + item.quantity, 0);
      const feePerShoe = order.isBulkDelivery ? 60 : 90;
      const products = order.orderItems.map(item => `${item.product.name} (${item.quantity})`).join('; ');
      
      return [
        order.orderNumber,
        order.user?.name || 'Unknown',
        order.user?.email || 'Unknown',
        order.deliveredAt?.toLocaleDateString() || order.updatedAt.toLocaleDateString(),
        shoesCount.toString(),
        order.isBulkDelivery ? 'Bulk (5+)' : 'Regular',
        `M${feePerShoe}`,
        `M${order.deliveryFee}`,
        order.deliveryFeePaid ? 'Paid' : 'Pending',
        order.deliveryFeePaidAt?.toLocaleDateString() || '',
        products
      ];
    });

    // Generate CSV content
    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    // Add summary at the end
    const totalFeesOwed = deliveredOrders.reduce((sum, order) => sum + (order.deliveryFee || 0), 0);
    const totalFeesPaid = deliveredOrders
      .filter(order => order.deliveryFeePaid)
      .reduce((sum, order) => sum + (order.deliveryFee || 0), 0);
    const pendingFees = totalFeesOwed - totalFeesPaid;

    const summary = [
      '',
      'SUMMARY',
      `Total Orders,${deliveredOrders.length}`,
      `Total Fees Owed,M${totalFeesOwed}`,
      `Total Fees Paid,M${totalFeesPaid}`,
      `Pending Fees,M${pendingFees}`,
      `Bulk Deliveries,${deliveredOrders.filter(o => o.isBulkDelivery).length}`,
      `Regular Deliveries,${deliveredOrders.filter(o => !o.isBulkDelivery).length}`,
      `Report Generated,${new Date().toLocaleString()}`
    ].join('\n');

    const finalCsvContent = csvContent + '\n' + summary;

    // Return CSV file
    return new NextResponse(finalCsvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="delivery-fees-report-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });
  } catch (error) {
    console.error("Error exporting delivery fees:", error);
    return NextResponse.json(
      { success: false, error: "Failed to export delivery fees" },
      { status: 500 }
    );
  }
}
