import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth-utils';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const minDiscrepancy = parseFloat(searchParams.get('minDiscrepancy') || '0');

    // Build where clause
    const where: any = {};
    if (minDiscrepancy > 0) {
      where.discrepancy = { gte: minDiscrepancy };
    }

    // Get validation logs
    const logs = await prisma.orderValidation.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    });

    // Get total count for pagination
    const totalCount = await prisma.orderValidation.count({ where });

    // Transform the data for the frontend
    const transformedLogs = logs.map(log => ({
      id: log.id,
      orderId: log.orderId,
      originalTotal: parseFloat(log.originalTotal.toString()),
      correctedTotal: parseFloat(log.correctedTotal.toString()),
      discrepancy: parseFloat(log.discrepancy.toString()),
      correctionType: log.correctionType,
      details: log.details,
      createdAt: log.createdAt.toISOString()
    }));

    // Calculate summary statistics
    const stats = {
      totalLogs: totalCount,
      averageDiscrepancy: logs.length > 0 
        ? logs.reduce((sum, log) => sum + parseFloat(log.discrepancy.toString()), 0) / logs.length 
        : 0,
      maxDiscrepancy: logs.length > 0 
        ? Math.max(...logs.map(log => parseFloat(log.discrepancy.toString()))) 
        : 0,
      commonCorrectionTypes: await getCommonCorrectionTypes()
    };

    return NextResponse.json({
      logs: transformedLogs,
      stats,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    });

  } catch (error) {
    console.error('Error fetching order validation logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch validation logs' },
      { status: 500 }
    );
  }
}

async function getCommonCorrectionTypes() {
  try {
    const result = await prisma.orderValidation.groupBy({
      by: ['correctionType'],
      _count: {
        correctionType: true
      },
      orderBy: {
        _count: {
          correctionType: 'desc'
        }
      },
      take: 5
    });

    return result.map(item => ({
      type: item.correctionType,
      count: item._count.correctionType
    }));
  } catch (error) {
    console.error('Error getting common correction types:', error);
    return [];
  }
}
