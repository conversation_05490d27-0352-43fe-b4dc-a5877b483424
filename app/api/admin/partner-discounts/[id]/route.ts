import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// PATCH - Update partner discount status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { isActive } = body;

    if (typeof isActive !== 'boolean') {
      return NextResponse.json(
        { error: 'isActive must be a boolean value' },
        { status: 400 }
      );
    }

    // Update the discount code
    const updatedDiscount = await prisma.discountCode.update({
      where: {
        id: id,
        description: {
          startsWith: 'PARTNER:'
        }
      },
      data: {
        isActive: isActive
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        id: updatedDiscount.id,
        isActive: updatedDiscount.isActive
      },
      message: `Partner discount ${isActive ? 'activated' : 'deactivated'} successfully`
    });

  } catch (error: any) {
    console.error('Error updating partner discount:', error);

    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Partner discount not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete partner discount
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Check if discount has been used
    const discount = await prisma.discountCode.findUnique({
      where: {
        id: id,
        description: {
          startsWith: 'PARTNER:'
        }
      }
    });

    if (!discount) {
      return NextResponse.json(
        { error: 'Partner discount not found' },
        { status: 404 }
      );
    }

    if (discount.usedCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete discount code that has been used' },
        { status: 400 }
      );
    }

    // Delete the discount code
    await prisma.discountCode.delete({
      where: { id: id }
    });

    return NextResponse.json({
      success: true,
      message: 'Partner discount deleted successfully'
    });

  } catch (error: any) {
    console.error('Error deleting partner discount:', error);

    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Partner discount not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
