import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// GET - Fetch all partner discount codes
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const partnerDiscounts = await prisma.discountCode.findMany({
      where: {
        description: {
          startsWith: 'PARTNER:'
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Transform data to include partner name
    const transformedDiscounts = partnerDiscounts.map(discount => ({
      id: discount.id,
      code: discount.code,
      partnerName: discount.description?.replace('PARTNER:', '') || 'Unknown',
      type: discount.type,
      value: discount.value,
      maxUses: discount.maxUses,
      usedCount: discount.usedCount,
      isActive: discount.isActive,
      validUntil: discount.validUntil?.toISOString(),
      createdAt: discount.createdAt.toISOString(),
    }));

    return NextResponse.json({
      success: true,
      data: transformedDiscounts
    });

  } catch (error) {
    console.error('Error fetching partner discounts:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new partner discount code
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { partnerName, type, value, maxUses, validUntil } = body;

    // Validate required fields
    if (!partnerName || !type || !value) {
      return NextResponse.json(
        { error: 'Partner name, type, and value are required' },
        { status: 400 }
      );
    }

    // Validate discount type
    if (!['PERCENTAGE', 'FIXED_AMOUNT'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid discount type' },
        { status: 400 }
      );
    }

    // Validate value
    if (value <= 0) {
      return NextResponse.json(
        { error: 'Discount value must be greater than 0' },
        { status: 400 }
      );
    }

    if (type === 'PERCENTAGE' && value > 100) {
      return NextResponse.json(
        { error: 'Percentage discount cannot exceed 100%' },
        { status: 400 }
      );
    }

    // Generate unique partner code
    const partnerCode = generatePartnerCode(partnerName);
    
    // Check if code already exists
    const existingCode = await prisma.discountCode.findUnique({
      where: { code: partnerCode }
    });

    if (existingCode) {
      return NextResponse.json(
        { error: 'A discount code for this partner already exists' },
        { status: 400 }
      );
    }

    // Create the discount code
    const discountCode = await prisma.discountCode.create({
      data: {
        code: partnerCode,
        description: `PARTNER:${partnerName}`,
        type: type as 'PERCENTAGE' | 'FIXED_AMOUNT',
        value: parseFloat(value),
        maxUses: maxUses ? parseInt(maxUses) : null,
        isActive: true,
        validUntil: validUntil ? new Date(validUntil) : null,
      }
    });

    // Transform response
    const transformedDiscount = {
      id: discountCode.id,
      code: discountCode.code,
      partnerName: partnerName,
      type: discountCode.type,
      value: discountCode.value,
      maxUses: discountCode.maxUses,
      usedCount: discountCode.usedCount,
      isActive: discountCode.isActive,
      validUntil: discountCode.validUntil?.toISOString(),
      createdAt: discountCode.createdAt.toISOString(),
    };

    return NextResponse.json({
      success: true,
      data: transformedDiscount,
      message: `Partner discount code created successfully: ${partnerCode}`
    });

  } catch (error) {
    console.error('Error creating partner discount:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to generate partner code
function generatePartnerCode(partnerName: string): string {
  // Clean partner name and create code
  const cleanName = partnerName
    .toUpperCase()
    .replace(/[^A-Z0-9]/g, '')
    .substring(0, 8);
  
  // Add random suffix to ensure uniqueness
  const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase();
  
  return `PARTNER_${cleanName}_${randomSuffix}`;
}
