import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { getPendingPayments, verifyLayBuyPayment } from "@/actions/layBuyPaymentActions";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// GET /api/admin/lay-buy-payments - Get pending payments for admin review
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    const result = await getPendingPayments({ page, limit });

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    // Transform the data for the admin interface
    const transformedPayments = result.data?.payments?.map((payment: any) => ({
      id: payment.id,
      amount: payment.amount,
      paymentType: payment.paymentType,
      paymentMethod: payment.paymentMethod,
      paymentProof: payment.paymentProof,
      notes: payment.notes,
      createdAt: payment.createdAt,
      order: {
        id: payment.layBuyOrder.id,
        orderNumber: payment.layBuyOrder.orderNumber,
        totalAmount: payment.layBuyOrder.totalAmount,
        amountPaid: payment.layBuyOrder.amountPaid,
        customer: {
          id: payment.layBuyOrder.user.id,
          name: payment.layBuyOrder.user.name,
          email: payment.layBuyOrder.user.email,
        },
      },
    }));

    const response: ApiResponse<{
      payments: typeof transformedPayments;
      pagination: any;
    }> = {
      success: true,
      data: {
        payments: transformedPayments ?? [],
        pagination: result.data?.pagination ?? { page: 1, limit: 20, total: 0 },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching pending payments:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch pending payments" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/lay-buy-payments - Verify or reject a payment
export async function PATCH(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { paymentId, status, adminNotes } = body;

    if (!paymentId || !status) {
      return NextResponse.json(
        { success: false, error: "Payment ID and status are required" },
        { status: 400 }
      );
    }

    if (!["VERIFIED", "REJECTED"].includes(status)) {
      return NextResponse.json(
        { success: false, error: "Invalid status. Must be VERIFIED or REJECTED" },
        { status: 400 }
      );
    }

    const result = await verifyLayBuyPayment(paymentId, user.id, status, adminNotes);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    const response: ApiResponse<typeof result.data> = {
      success: true,
      data: result.data,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error verifying payment:", error);
    return NextResponse.json(
      { success: false, error: "Failed to verify payment" },
      { status: 500 }
    );
  }
}
