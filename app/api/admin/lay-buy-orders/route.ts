import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { getAllLayBuyOrders } from "@/actions/layBuyActions";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// GET /api/admin/lay-buy-orders - Get all Lay-Buy orders for admin
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status") || undefined;
    const search = searchParams.get("search") || undefined;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    const result = await getAllLayBuyOrders({
      status,
      search,
      page,
      limit,
    });

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    // Transform the data for the admin interface
    const transformedOrders = result.data?.orders?.map((order: any) => ({
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      totalAmount: order.totalAmount,
      amountPaid: order.amountPaid,
      remainingAmount: order.remainingAmount,
      dueDate: order.dueDate,
      gracePeriodEnd: order.gracePeriodEnd,
      createdAt: order.createdAt,
      customer: {
        id: order.user.id,
        name: order.user.name,
        email: order.user.email,
      },
      itemCount: order.orderItems.length,
      paymentCount: order._count.payments,
      reminderCount: order._count.reminders,
    })) ?? [];

    const response: ApiResponse<{
      orders: typeof transformedOrders;
      pagination: any;
    }> = {
      success: true,
      data: {
        orders: transformedOrders,
        pagination: result.data && result.data.pagination ? result.data.pagination : null,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching admin Lay-Buy orders:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch Lay-Buy orders" },
      { status: 500 }
    );
  }
}
