import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(request: NextRequest, context: any) {
  const orderId = context.params.id;
  try {
    const order = await prisma.layBuyOrder.findUnique({ where: { id: orderId } });
    if (!order) {
      return NextResponse.json({ success: false, error: 'Order not found' }, { status: 404 });
    }
    await prisma.layBuyOrder.update({
      where: { id: orderId },
      data: { status: 'ACTIVE', updatedAt: new Date() },
    });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error unpausing Lay-Buy order:', error);
    return NextResponse.json({ success: false, error: 'Failed to unpause order' }, { status: 500 });
  }
} 