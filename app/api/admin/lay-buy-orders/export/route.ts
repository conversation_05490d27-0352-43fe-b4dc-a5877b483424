import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { getAllLayBuyOrders } from "@/actions/layBuyActions";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// GET /api/admin/lay-buy-orders/export - Export Lay-Buy orders data
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const format = searchParams.get("format") || "csv";
    const status = searchParams.get("status") || undefined;
    const startDate = searchParams.get("startDate") || undefined;
    const endDate = searchParams.get("endDate") || undefined;

    // Get all orders without pagination for export
    const result = await getAllLayBuyOrders({
      status,
      page: 1,
      limit: 10000, // Large limit to get all orders
    });

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    let filteredOrders = result?.data?.orders ?? [];

    // Apply date filters if provided
    if (startDate || endDate) {
      filteredOrders = filteredOrders.filter((order: any) => {
        const orderDate = new Date(order.createdAt);
        if (startDate && orderDate < new Date(startDate)) return false;
        if (endDate && orderDate > new Date(endDate)) return false;
        return true;
      });
    }

    if (format === "csv") {
      // Generate CSV
      const csvHeaders = [
        "Order Number",
        "Customer Name",
        "Customer Email",
        "Status",
        "Total Amount",
        "Amount Paid",
        "Remaining Balance",
        "Due Date",
        "Grace Period End",
        "Created Date",
        "Completed Date",
        "Cancelled Date",
        "Forfeited Date",
        "Refund Amount",
        "Items Count",
        "Payments Count",
        "Reminders Count",
      ];

      const csvRows = filteredOrders.map((order: any) => [
        order.orderNumber,
        order.user.name,
        order.user.email,
        order.status,
        order.totalAmount.toFixed(2),
        order.amountPaid.toFixed(2),
        (order.totalAmount - order.amountPaid).toFixed(2),
        new Date(order.dueDate).toLocaleDateString(),
        new Date(order.gracePeriodEnd).toLocaleDateString(),
        new Date(order.createdAt).toLocaleDateString(),
        order.completedAt ? new Date(order.completedAt).toLocaleDateString() : "",
        order.cancelledAt ? new Date(order.cancelledAt).toLocaleDateString() : "",
        order.forfeitedAt ? new Date(order.forfeitedAt).toLocaleDateString() : "",
        order.refundAmount ? order.refundAmount.toFixed(2) : "",
        order.orderItems.length,
        order._count.payments,
        order._count.reminders,
      ]);

      const csvContent = [
        csvHeaders.join(","),
        ...csvRows.map(row => row.map(cell => `"${cell}"`).join(","))
      ].join("\n");

      return new NextResponse(csvContent, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="lay-buy-orders-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    } else if (format === "json") {
      // Generate JSON
      const jsonData = {
        exportDate: new Date().toISOString(),
        totalOrders: filteredOrders.length,
        filters: {
          status,
          startDate,
          endDate,
        },
        summary: {
          totalValue: filteredOrders.reduce((sum: number, order: any) => sum + order.totalAmount, 0),
          totalPaid: filteredOrders.reduce((sum: number, order: any) => sum + order.amountPaid, 0),
          totalOutstanding: filteredOrders.reduce((sum: number, order: any) => sum + (order.totalAmount - order.amountPaid), 0),
          statusBreakdown: filteredOrders.reduce((acc: any, order: any) => {
            acc[order.status] = (acc[order.status] || 0) + 1;
            return acc;
          }, {}),
        },
        orders: filteredOrders.map((order: any) => ({
          orderNumber: order.orderNumber,
          customer: {
            name: order.user.name,
            email: order.user.email,
          },
          status: order.status,
          amounts: {
            total: order.totalAmount,
            paid: order.amountPaid,
            remaining: order.totalAmount - order.amountPaid,
            refund: order.refundAmount,
          },
          dates: {
            created: order.createdAt,
            due: order.dueDate,
            gracePeriodEnd: order.gracePeriodEnd,
            completed: order.completedAt,
            cancelled: order.cancelledAt,
            forfeited: order.forfeitedAt,
          },
          items: order.orderItems.map((item: any) => ({
            productName: item.product.name,
            quantity: item.quantity,
            price: item.price,
            size: item.size,
            color: item.color,
          })),
          statistics: {
            paymentsCount: order._count.payments,
            remindersCount: order._count.reminders,
          },
        })),
      };

      return new NextResponse(JSON.stringify(jsonData, null, 2), {
        headers: {
          "Content-Type": "application/json",
          "Content-Disposition": `attachment; filename="lay-buy-orders-${new Date().toISOString().split('T')[0]}.json"`,
        },
      });
    } else {
      return NextResponse.json(
        { success: false, error: "Invalid format. Supported formats: csv, json" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error exporting Lay-Buy orders:", error);
    return NextResponse.json(
      { success: false, error: "Failed to export Lay-Buy orders" },
      { status: 500 }
    );
  }
}
