import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { ApiResponse } from "@/utils/types";
import { sendOrderStatusUpdateEmail } from "@/lib/email-service";
import { getFeeSettings } from "@/lib/fee-utils";

// PATCH /api/admin/orders/[id]/status - Update order status
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const { id: paramsId } = await context.params;
    const body = await request.json();
    const { status, trackingNumber, trackingUrl, adminNotes } = body;

    if (!status) {
      return NextResponse.json(
        { success: false, error: "Status is required" },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ["PENDING", "PAID", "CONFIRMED", "PROCESSING", "SHIPPED", "DELIVERED", "CANCELLED"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: "Invalid status" },
        { status: 400 }
      );
    }

    // Get the current order to check previous status
    const currentOrder = await prisma.order.findUnique({
      where: { id: paramsId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                name: true,
                images: true,
              },
            },
          },
        },
      },
    });

    if (!currentOrder) {
      return NextResponse.json(
        { success: false, error: "Order not found" },
        { status: 404 }
      );
    }

    // Calculate delivery fee if status is being changed to DELIVERED
    let deliveryFeeData = {};
    if (status === "DELIVERED" && currentOrder.status !== "DELIVERED") {
      const deliveryFee = await calculateDeliveryFee(currentOrder);
      deliveryFeeData = {
        deliveryFee: deliveryFee.fee,
        isBulkDelivery: deliveryFee.isBulk,
        deliveredAt: new Date(),
      };
    }

    // Update the order
    const updatedOrder = await prisma.order.update({
      where: { id: paramsId },
      data: {
        status,
        trackingNumber: trackingNumber || currentOrder.trackingNumber,
        trackingUrl: trackingUrl || currentOrder.trackingUrl,
        adminNotes: adminNotes || currentOrder.adminNotes,
        updatedAt: new Date(),
        ...deliveryFeeData,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                name: true,
                images: true,
              },
            },
          },
        },
      },
    });

    // Send status update email to customer if status changed
    if (currentOrder.status !== status && updatedOrder.user?.email) {
      try {
        // Create a properly typed order object for the email function
        const orderForEmail = {
          id: updatedOrder.id,
          orderNumber: updatedOrder.orderNumber,
          totalAmount: updatedOrder.totalAmount,
          user: updatedOrder.user,
          orderItems: updatedOrder.orderItems.map(item => ({
            quantity: item.quantity,
            price: item.price,
            size: item.size,
            product: {
              name: item.product.name,
            },
          })),
        };
        await sendOrderStatusUpdateEmail(orderForEmail, currentOrder.status);
      } catch (emailError) {
        console.error("Failed to send status update email:", emailError);
        // Don't fail the request if email fails
      }
    }

    const response: ApiResponse<typeof updatedOrder> = {
      success: true,
      data: updatedOrder,
      message: "Order status updated successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating order status:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update order status" },
      { status: 500 }
    );
  }
}

// Calculate delivery fee based on bulk delivery logic
async function calculateDeliveryFee(order: any) {
  const feeSettings = await getFeeSettings();
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // Count total shoes being delivered today (including this order)
  const ordersDeliveredToday = await prisma.order.findMany({
    where: {
      status: "DELIVERED",
      deliveredAt: {
        gte: today,
        lt: tomorrow,
      },
    },
    include: {
      orderItems: true,
    },
  });

  // Calculate total shoes delivered today (including current order)
  let totalShoesToday = 0;
  for (const deliveredOrder of ordersDeliveredToday) {
    totalShoesToday += deliveredOrder.orderItems.reduce((sum, item) => sum + item.quantity, 0);
  }

  // Add shoes from current order
  const currentOrderShoes = order.orderItems.reduce((sum: number, item: any) => sum + item.quantity, 0);
  totalShoesToday += currentOrderShoes;

  // Determine if this qualifies for bulk delivery
  const isBulkDelivery = totalShoesToday >= feeSettings.bulkDeliveryThreshold;
  const feePerShoe = isBulkDelivery ? feeSettings.defaultBulkDeliveryFee : feeSettings.defaultDeliveryFee;
  const totalFee = feePerShoe * currentOrderShoes;

  // If this order triggers bulk delivery, update all orders delivered today
  if (isBulkDelivery && ordersDeliveredToday.length > 0) {
    for (const deliveredOrder of ordersDeliveredToday) {
      if (!deliveredOrder.isBulkDelivery) {
        const orderShoes = deliveredOrder.orderItems.reduce((sum, item) => sum + item.quantity, 0);
        const newFee = feeSettings.defaultBulkDeliveryFee * orderShoes; // Update to bulk rate

        await prisma.order.update({
          where: { id: deliveredOrder.id },
          data: {
            deliveryFee: newFee,
            isBulkDelivery: true,
          },
        });
      }
    }
  }

  return {
    fee: totalFee,
    isBulk: isBulkDelivery,
    shoesCount: currentOrderShoes,
    totalShoesToday,
  };
}
