import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);
const visionModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

export async function POST(request: NextRequest) {
  try {
    const { imageUrl, productType } = await request.json();

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    const prompt = `Validate this product image for an e-commerce listing. 
    Expected product type: ${productType || 'footwear'}
    
    Check for:
    1. Is this actually the expected product type?
    2. Is the image clear and well-lit?
    3. Is the product the main focus?
    4. Is the background appropriate for e-commerce?
    5. Are there any inappropriate elements?
    6. Is the image professional quality?
    
    Provide:
    - Valid: true/false
    - Score: 0-100 (overall quality score)
    - Issues: list any problems found
    
    Format:
    Valid: [true/false]
    Score: [0-100]
    Issues: [list issues separated by commas, or "None"]`;

    const result = await visionModel.generateContent([
      {
        inlineData: {
          data: await getImageAsBase64(imageUrl),
          mimeType: 'image/jpeg'
        }
      },
      prompt
    ]);

    const response = await result.response;
    const validationText = response.text();
    
    // Parse the validation result
    const lines = validationText.split('\n');
    let isValid = false;
    let score = 0;
    let issues: string[] = [];

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('Valid:')) {
        const validText = trimmedLine.replace('Valid:', '').trim().toLowerCase();
        isValid = validText === 'true';
      } else if (trimmedLine.startsWith('Score:')) {
        const scoreMatch = trimmedLine.match(/\d+/);
        if (scoreMatch) {
          score = parseInt(scoreMatch[0]);
        }
      } else if (trimmedLine.startsWith('Issues:')) {
        const issuesText = trimmedLine.replace('Issues:', '').trim();
        if (issuesText.toLowerCase() !== 'none') {
          issues = issuesText.split(',').map(issue => issue.trim()).filter(issue => issue.length > 0);
        }
      }
    }

    return NextResponse.json({
      validation: {
        isValid,
        issues,
        score: score / 100 // Convert to 0-1 scale
      }
    });
  } catch (error) {
    console.error('Error validating image:', error);
    return NextResponse.json(
      { 
        error: 'Failed to validate image',
        validation: { isValid: false, issues: ['Validation failed'], score: 0 }
      },
      { status: 500 }
    );
  }
}

async function getImageAsBase64(imageUrl: string): Promise<string> {
  try {
    const response = await fetch(imageUrl);
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error('Error fetching image:', error);
    throw error;
  }
}
