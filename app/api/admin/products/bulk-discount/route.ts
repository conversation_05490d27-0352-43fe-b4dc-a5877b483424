import { NextRequest, NextResponse } from "next/server";
import { ApiResponse } from "@/utils/types";
import { requireAdmin } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";

// POST /api/admin/products/bulk-discount - Apply discount to multiple products
// Request body: { discountType: 'PERCENTAGE' | 'FIXED_AMOUNT', value: number, categoryId?: string, applyToAll: boolean }
export async function POST(request: NextRequest) {
  try {
    // Check admin access
    await requireAdmin();

    const body = await request.json();
    const { discountType, value, categoryId, applyToAll = false } = body;

    // Validate request
    if (!discountType || (discountType !== 'PERCENTAGE' && discountType !== 'FIXED_AMOUNT')) {
      return NextResponse.json(
        { success: false, error: "Invalid or missing discount type. Must be 'PERCENTAGE' or 'FIXED_AMOUNT'" },
        { status: 400 }
      );
    }

    if (value === undefined || value === null || isNaN(Number(value)) || Number(value) <= 0) {
      return NextResponse.json(
        { success: false, error: "Invalid or missing discount value. Must be a positive number" },
        { status: 400 }
      );
    }

    if (!applyToAll && !categoryId) {
      return NextResponse.json(
        { success: false, error: "Either categoryId must be provided or applyToAll must be true" },
        { status: 400 }
      );
    }

    // Build the where clause
    const where: any = { isActive: true }; // Only apply to active products
    
    if (!applyToAll && categoryId) {
      // Verify category exists
      const category = await prisma.category.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { success: false, error: `Category with ID ${categoryId} not found` },
          { status: 404 }
        );
      }
      where.categoryId = categoryId;
    }

    // Get all products that match the criteria
    const products = await prisma.product.findMany({
      where,
      select: { id: true, price: true, discountedPrice: true }
    });

    if (products.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: applyToAll 
            ? "No active products found" 
            : `No active products found in the specified category`
        },
        { status: 404 }
      );
    }

    // Calculate new discounted prices
    const updates = products.map(product => {
      let newDiscountedPrice: number | null = null;
      
      if (discountType === 'PERCENTAGE') {
        const discountAmount = (product.price * value) / 100;
        newDiscountedPrice = Math.max(0, product.price - discountAmount);
      } else { // FIXED_AMOUNT
        newDiscountedPrice = Math.max(0, product.price - value);
      }

      // If the discounted price is the same as the original, set to null
      if (Math.abs(newDiscountedPrice - product.price) < 0.01) {
        newDiscountedPrice = null;
      }

      return prisma.product.update({
        where: { id: product.id },
        data: { discountedPrice: newDiscountedPrice }
      });
    });

    // Execute all updates in a transaction
    const results = await prisma.$transaction(updates);

    // Log the discount application
    await prisma.discountLog.create({
      data: {
        type: discountType,
        value,
        categoryId: applyToAll ? null : categoryId,
        productsAffected: results.length,
        appliedById: (await requireAdmin()).id
      }
    });

    const response: ApiResponse = {
      success: true,
      data: {
        message: `Successfully applied discount to ${results.length} products`,
        productsAffected: results.length,
        discountType,
        value
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error applying bulk discount:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to apply discount",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/products/bulk-discount/history - Get history of bulk discounts
export async function GET(request: NextRequest) {
  try {
    await requireAdmin();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const skip = (page - 1) * limit;

    const [discounts, total] = await Promise.all([
      prisma.discountLog.findMany({
        include: {
          category: { select: { name: true } },
          appliedBy: { select: { name: true, email: true } }
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.discountLog.count()
    ]);

    const response: ApiResponse = {
      success: true,
      data: {
        data: discounts,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        }
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching discount history:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch discount history" },
      { status: 500 }
    );
  }
}
