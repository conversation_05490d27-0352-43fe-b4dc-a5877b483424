import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from "@google/generative-ai";

// API Key rotation for rate limit management
class APIKeyManager {
  public keys: string[] = []; // Make public so we can access length
  public currentIndex = 0; // Make public for logging
  private keyErrors: Map<string, number> = new Map(); // Track errors per key
  
  constructor() {
    // Collect all available API keys from environment
    const baseKey = process.env.GOOGLE_AI_API_KEY;
    if (baseKey) this.keys.push(baseKey);
    
    // Add numbered keys (GOOGLE_AI_API_KEY_2, GOOGLE_AI_API_KEY_3, etc.)
    for (let i = 2; i <= 10; i++) {
      const key = process.env[`GOOGLE_AI_API_KEY_${i}`];
      if (key) this.keys.push(key);
    }
    
    console.log(`🔑 Loaded ${this.keys.length} API keys for rotation`);
  }
  
  getCurrentKey(): string {
    if (this.keys.length === 0) {
      throw new Error('No Google AI API keys available');
    }
    return this.keys[this.currentIndex];
  }
  
  rotateToNextKey(): string {
    this.currentIndex = (this.currentIndex + 1) % this.keys.length;
    console.log(`🔄 Rotated to API key ${this.currentIndex + 1}/${this.keys.length}`);
    return this.getCurrentKey();
  }
  
  markKeyError(key: string) {
    const errorCount = this.keyErrors.get(key) || 0;
    this.keyErrors.set(key, errorCount + 1);
    
    // If this key has too many errors, try the next one
    if (errorCount >= 2) {
      console.log(`⚠️  Key ${this.currentIndex + 1} has multiple errors, rotating...`);
      this.rotateToNextKey();
    }
  }
}

const keyManager = new APIKeyManager();

export async function POST(request: NextRequest) {
  // Read the request body once at the beginning
  const { imageUrl } = await request.json();

  if (!imageUrl) {
    return NextResponse.json(
      { error: 'Image URL is required' },
      { status: 400 }
    );
  }

  let attempts = 0;
  const maxAttempts = keyManager.keys.length; // Try each key once
  
  while (attempts < maxAttempts) {
    try {
      // Get current API key and create model
      const currentKey = keyManager.getCurrentKey();
      const genAI = new GoogleGenerativeAI(currentKey);
      const visionModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

      console.log(`🔑 Using API key ${keyManager.currentIndex + 1}/${keyManager.keys.length}`);

      const prompt = `Analyze this product image and provide a quality assessment. 
      Evaluate:
      1. Image clarity and sharpness (0-10)
      2. Lighting quality (0-10)
      3. Product positioning and framing (0-10)
      4. Background appropriateness (0-10)
      5. Overall professional appearance (0-10)
      
      Provide:
      - Overall score (0-100)
      - List of specific issues found
      - Suggestions for improvement
      - Quality rating (low/medium/high)
      
      Format your response as:
      Score: [number]
      Issues: [list issues separated by commas, or "None" if no issues]
      Suggestions: [list suggestions separated by commas]
      Quality: [low/medium/high]`;

      const result = await visionModel.generateContent([
        {
          inlineData: {
            data: await getImageAsBase64(imageUrl),
            mimeType: 'image/jpeg'
          }
        },
        prompt
      ]);

      const response = await result.response;
      const analysisText = response.text();
      
      // Parse the analysis
      const lines = analysisText.split('\n');
      let score = 50; // default
      let issues: string[] = [];
      let suggestions: string[] = [];
      let estimatedQuality: 'low' | 'medium' | 'high' = 'medium';

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('Score:')) {
          const scoreMatch = trimmedLine.match(/\d+/);
          if (scoreMatch) {
            score = parseInt(scoreMatch[0]);
          }
        } else if (trimmedLine.startsWith('Issues:')) {
          const issuesText = trimmedLine.replace('Issues:', '').trim();
          if (issuesText.toLowerCase() !== 'none') {
            issues = issuesText.split(',').map((issue: string) => issue.trim()).filter((issue: string) => issue.length > 0);
          }
        } else if (trimmedLine.startsWith('Suggestions:')) {
          const suggestionsText = trimmedLine.replace('Suggestions:', '').trim();
          suggestions = suggestionsText.split(',').map((suggestion: string) => suggestion.trim()).filter((suggestion: string) => suggestion.length > 0);
        } else if (trimmedLine.startsWith('Quality:')) {
          const qualityText = trimmedLine.replace('Quality:', '').trim().toLowerCase();
          if (['low', 'medium', 'high'].includes(qualityText)) {
            estimatedQuality = qualityText as 'low' | 'medium' | 'high';
          }
        }
      }

      console.log(`✅ Successfully analyzed image with API key ${keyManager.currentIndex + 1}`);
      return NextResponse.json({
        score: score / 100, // Convert to 0-1 scale
        issues,
        suggestions,
        estimatedQuality
      });
      
    } catch (error: any) {
      console.error(`❌ Attempt ${attempts + 1} failed with key ${keyManager.currentIndex + 1}:`, error.message);
      
      // Check for specific error types
      const isRateLimit = error.message?.includes('429') || 
                         error.message?.includes('quota') || 
                         error.message?.includes('Too Many Requests');
      
      const isBlocked = error.message?.includes('403') || 
                       error.message?.includes('Forbidden') || 
                       error.message?.includes('API_KEY_SERVICE_BLOCKED');
      
      if (isRateLimit || isBlocked) {
        if (isBlocked) {
          console.log(`🚫 API key ${keyManager.currentIndex + 1} is blocked or invalid, rotating...`);
        } else {
          console.log(`⏳ API key ${keyManager.currentIndex + 1} hit rate limit, rotating...`);
        }
        
        keyManager.markKeyError(keyManager.getCurrentKey());
        
        // If we have more keys to try, rotate and retry
        if (attempts < maxAttempts - 1) {
          keyManager.rotateToNextKey();
          attempts++;
          continue;
        }
      }
      
      // For other errors or if all keys exhausted
      attempts++;
      if (attempts >= maxAttempts) {
        console.error('🚨 All API keys exhausted or failed');
        return NextResponse.json(
          { 
            error: 'All API keys failed. Please check your API keys and quotas.',
            score: 0.5,
            issues: ['Analysis temporarily unavailable - API issues'],
            suggestions: ['Please check API keys and try again later'],
            estimatedQuality: 'medium'
          },
          { status: 503 } // Service Unavailable
        );
      }
    }
  }
  
  // Fallback error response
  return NextResponse.json(
    { 
      error: 'Failed to analyze image after all attempts',
      score: 0.5,
      issues: ['Analysis failed'],
      suggestions: ['Please try again'],
      estimatedQuality: 'medium'
    },
    { status: 500 }
  );
}

async function getImageAsBase64(imageUrl: string): Promise<string> {
  try {
    const response = await fetch(imageUrl);
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error('Error fetching image:', error);
    throw error;
  }
}
