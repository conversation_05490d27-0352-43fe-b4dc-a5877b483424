import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);
const visionModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

export async function POST(request: NextRequest) {
  try {
    const { imageUrl } = await request.json();

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    const prompt = `Analyze this product image for any defects or quality issues. 
    Look for:
    - Blurriness or poor focus
    - Bad lighting (too dark, too bright, harsh shadows)
    - Image quality issues (pixelation, compression artifacts)
    - Product positioning issues (cut off, poor angle)
    - Background issues (distracting, unprofessional)
    - Color accuracy issues
    - Any visible damage or wear on the product
    
    List each defect found. If no defects are found, return "No defects detected".
    Be concise and specific.`;

    const result = await visionModel.generateContent([
      {
        inlineData: {
          data: await getImageAsBase64(imageUrl),
          mimeType: 'image/jpeg'
        }
      },
      prompt
    ]);

    const response = await result.response;
    const defectsText = response.text();
    
    // Parse the defects
    let defects: string[] = [];
    if (defectsText.toLowerCase().includes('no defects detected')) {
      defects = [];
    } else {
      defects = defectsText
        .split('\n')
        .map(defect => defect.trim())
        .filter(defect => defect.length > 0 && !defect.startsWith('-'))
        .map(defect => defect.replace(/^[-•*]\s*/, ''));
    }

    return NextResponse.json({ defects });
  } catch (error) {
    console.error('Error detecting image defects:', error);
    return NextResponse.json(
      { error: 'Failed to detect defects', defects: [] },
      { status: 500 }
    );
  }
}

async function getImageAsBase64(imageUrl: string): Promise<string> {
  try {
    const response = await fetch(imageUrl);
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error('Error fetching image:', error);
    throw error;
  }
}
