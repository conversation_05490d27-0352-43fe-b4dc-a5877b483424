import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

// Helper function to require admin access
async function requireAdmin() {
  const user = await getCurrentUser();
  if (!user || user.role !== "ADMIN") {
    throw new Error("Admin access required");
  }
  return user;
}

// POST /api/admin/products/[id]/replace-image - Replace a specific product image
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await context.params;
    
    // Check admin access
    await requireAdmin();

    const body = await request.json();
    const { imageIndex, newImageUrl } = body;

    // Validate required fields
    if (typeof imageIndex !== "number" || imageIndex < 0) {
      return NextResponse.json(
        { success: false, error: "Valid image index is required" },
        { status: 400 }
      );
    }

    if (!newImageUrl || typeof newImageUrl !== "string") {
      return NextResponse.json(
        { success: false, error: "New image URL is required" },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      new URL(newImageUrl);
    } catch {
      return NextResponse.json(
        { success: false, error: "Invalid image URL format" },
        { status: 400 }
      );
    }

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, images: true, name: true },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    // Validate image index
    if (imageIndex >= existingProduct.images.length) {
      return NextResponse.json(
        { success: false, error: "Image index out of range" },
        { status: 400 }
      );
    }

    // Create new images array with the replaced image
    const newImages = [...existingProduct.images];
    const oldImageUrl = newImages[imageIndex];
    newImages[imageIndex] = newImageUrl;

    // Update the product with the new images array
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: {
        images: newImages,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        images: true,
        updatedAt: true,
      },
    });

    const response: ApiResponse<typeof updatedProduct> = {
      success: true,
      data: updatedProduct,
      message: `Image ${imageIndex + 1} replaced successfully`,
    };

    console.log(`[ADMIN] Image replaced for product ${existingProduct.name} (${productId}): Position ${imageIndex + 1}, Old: ${oldImageUrl}, New: ${newImageUrl}`);

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error replacing product image:", error);
    
    if (error instanceof Error && error.message === "Admin access required") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to replace image" },
      { status: 500 }
    );
  }
}
