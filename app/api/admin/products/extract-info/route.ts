import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);
const visionModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

export async function POST(request: NextRequest) {
  try {
    const { imageUrl } = await request.json();

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    const prompt = `Analyze this product image and extract the following information:
    - Brand name (if visible)
    - Product model/name
    - Colorway/color description
    - Product category (sneakers, boots, dress shoes, etc.)
    - Confidence level (0-100) in the identification
    
    Format your response as:
    Brand: [brand name or "Unknown"]
    Model: [model name or "Unknown"]
    Colorway: [color description]
    Category: [product category]
    Confidence: [0-100]
    
    Be specific and accurate. If you can't identify something, use "Unknown".`;

    const result = await visionModel.generateContent([
      {
        inlineData: {
          data: await getImageAsBase64(imageUrl),
          mimeType: 'image/jpeg'
        }
      },
      prompt
    ]);

    const response = await result.response;
    const analysisText = response.text();
    
    // Parse the response
    const lines = analysisText.split('\n');
    let brand: string | undefined;
    let model: string | undefined;
    let colorway: string | undefined;
    let category: string | undefined;
    let confidence = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('Brand:')) {
        const brandText = trimmedLine.replace('Brand:', '').trim();
        if (brandText.toLowerCase() !== 'unknown') {
          brand = brandText;
        }
      } else if (trimmedLine.startsWith('Model:')) {
        const modelText = trimmedLine.replace('Model:', '').trim();
        if (modelText.toLowerCase() !== 'unknown') {
          model = modelText;
        }
      } else if (trimmedLine.startsWith('Colorway:')) {
        colorway = trimmedLine.replace('Colorway:', '').trim();
      } else if (trimmedLine.startsWith('Category:')) {
        category = trimmedLine.replace('Category:', '').trim();
      } else if (trimmedLine.startsWith('Confidence:')) {
        const confidenceMatch = trimmedLine.match(/\d+/);
        if (confidenceMatch) {
          confidence = parseInt(confidenceMatch[0]);
        }
      }
    }

    return NextResponse.json({
      productInfo: {
        brand,
        model,
        colorway,
        category,
        confidence: confidence / 100 // Convert to 0-1 scale
      }
    });
  } catch (error) {
    console.error('Error extracting product info:', error);
    return NextResponse.json(
      { 
        error: 'Failed to extract product info',
        productInfo: { confidence: 0 }
      },
      { status: 500 }
    );
  }
}

async function getImageAsBase64(imageUrl: string): Promise<string> {
  try {
    const response = await fetch(imageUrl);
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error('Error fetching image:', error);
    throw error;
  }
}
