"use client";

import { useSession } from "@/lib/auth-client";
import NavBar from "@/components/navbar";
import ContactForm from "@/components/contact/contact-form";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MapPin, Phone, Mail, Clock } from "lucide-react";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { User } from "@/utils/types";
import { useEffect, useState } from "react";
import { getUserById } from "@/actions/userActions";
import NavBarSkeleton from "@/components/navBarSkeleton";

export default function ContactPage() {
  const { data: session, isPending } = useSession();
  const [userWithRole, setUserWithRole] = useState<User | null>(null);
  const [isLoadingUser, setIsLoadingUser] = useState(false);

  useEffect(() => {
    const getUserDetails = async () => {
      if (session?.user && !isLoadingUser) {
        setIsLoadingUser(true);
        try {
          const userResponse = await getUserById(session.user.id);
          if (userResponse.success && userResponse.data) {
            setUserWithRole(userResponse.data as User);
          }
        } catch (error) {
          console.error("Error fetching user details:", error);
        } finally {
          setIsLoadingUser(false);
        }
      }
    };

    getUserDetails();
  }, [session?.user?.id, isLoadingUser]);

  // if (isPending) {
  //   return (
  //     <div className="w-full h-screen flex items-center justify-center">
  //       <SpinnerCircle4 />
  //     </div>
  //   );
  // }

  return (
    <div className="min-h-screen bg-gray-50">
      {userWithRole ? (
        <NavBar
          user={userWithRole || (session?.user as User)}
          loading={isPending}
        />
      ) : (
        <NavBarSkeleton user={null} loading={isPending} />
      )}

      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Contact Us
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Have a question or need help? We're here to assist you. Send us a
              message and we'll get back to you as soon as possible.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact Information */}
            <div className="lg:col-span-1 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Get in Touch</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <Phone className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">
                        Phone/WhatsApp
                      </h3>
                      <p className="text-gray-600">+266 62844473</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <Mail className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">
                        Email
                      </h3>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* FAQ Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Frequently Asked Questions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">
                      How long does shipping take?
                    </h4>
                    <p className="text-sm text-gray-600">
                      Standard delivery takes 3-5 business days within Malawi.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">
                      What payment methods do you accept?
                    </h4>
                    <p className="text-sm text-gray-600">
                      We accept bank transfers and mobile money payments.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Send us a Message</CardTitle>
                </CardHeader>
                <CardContent>
                  <ContactForm user={userWithRole || undefined} />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
