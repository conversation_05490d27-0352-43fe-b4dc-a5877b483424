import prisma from "@/lib/prisma";

export interface FeeSettings {
  defaultShippingFee: number;
  defaultLateCollectionFee: number;
  defaultDeliveryFee: number;
  defaultBulkDeliveryFee: number;
  bulkDeliveryThreshold: number;
}

export interface FeeCalculation {
  shippingFee: number;
  lateCollectionFee: number;
  deliveryFee: number;
  bulkDeliveryDiscount: number;
  totalFees: number;
  isBulkDelivery: boolean;
}

/**
 * Get current fee settings from database or return defaults
 */
export async function getFeeSettings(): Promise<FeeSettings> {
  try {
    const settings = await prisma.settings.findFirst();
    
    return {
      defaultShippingFee: settings?.defaultShippingFee ?? 100,
      defaultLateCollectionFee: settings?.defaultLateCollectionFee ?? 10,
      defaultDeliveryFee: settings?.defaultDeliveryFee ?? 90,
      defaultBulkDeliveryFee: settings?.defaultBulkDeliveryFee ?? 60,
      bulkDeliveryThreshold: settings?.bulkDeliveryThreshold ?? 5,
    };
  } catch (error) {
    console.error("Error fetching fee settings:", error);
    // Return defaults if database error
    return {
      defaultShippingFee: 100,
      defaultLateCollectionFee: 10,
      defaultDeliveryFee: 90,
      defaultBulkDeliveryFee: 60,
      bulkDeliveryThreshold: 5,
    };
  }
}

/**
 * Calculate delivery fee based on number of shoes and bulk delivery rules
 */
export function calculateDeliveryFee(
  shoesCount: number, 
  feeSettings: FeeSettings
): { fee: number; isBulk: boolean; discount: number } {
  const isBulk = shoesCount >= feeSettings.bulkDeliveryThreshold;
  const fee = isBulk ? feeSettings.defaultBulkDeliveryFee : feeSettings.defaultDeliveryFee;
  const regularFee = feeSettings.defaultDeliveryFee * shoesCount;
  const actualFee = fee * shoesCount;
  const discount = isBulk ? regularFee - actualFee : 0;

  return {
    fee: actualFee,
    isBulk,
    discount,
  };
}

/**
 * Calculate total fees for an order
 */
export function calculateOrderFees(
  orderItems: Array<{
    quantity: number;
    shippingFee?: number | null;
    lateCollectionFee?: number | null;
  }>,
  deliveryFee: number,
  isBulkDelivery: boolean,
  feeSettings: FeeSettings
): FeeCalculation {
  const totalShoes = orderItems.reduce((sum, item) => sum + item.quantity, 0);
  
  const shippingFees = orderItems.reduce((sum, item) => 
    sum + ((item.shippingFee || 0) * item.quantity), 0);
  
  const lateCollectionFees = orderItems.reduce((sum, item) => 
    sum + ((item.lateCollectionFee || 0) * item.quantity), 0);

  const bulkDeliveryDiscount = isBulkDelivery ? 
    (feeSettings.defaultDeliveryFee - feeSettings.defaultBulkDeliveryFee) * totalShoes : 0;

  const totalFees = shippingFees + deliveryFee + lateCollectionFees;

  return {
    shippingFee: shippingFees,
    lateCollectionFee: lateCollectionFees,
    deliveryFee,
    bulkDeliveryDiscount,
    totalFees,
    isBulkDelivery,
  };
}

/**
 * Calculate profit for an order item
 */
export function calculateItemProfit(
  salePrice: number,
  quantity: number,
  costPrice: number,
  shippingFee: number,
  lateCollectionFee: number
): number {
  const totalCost = (costPrice + shippingFee + lateCollectionFee) * quantity;
  const totalRevenue = salePrice * quantity;
  return totalRevenue - totalCost;
}

/**
 * Calculate total profit for an order
 */
export function calculateOrderProfit(
  totalAmount: number,
  orderItems: Array<{
    quantity: number;
    price: number;
    costPrice?: number | null;
    shippingFee?: number | null;
    lateCollectionFee?: number | null;
  }>
): number {
  const totalCost = orderItems.reduce((sum, item) => {
    const costPrice = item.costPrice || 0;
    const shippingFee = item.shippingFee || 0;
    const lateCollectionFee = item.lateCollectionFee || 0;
    const itemTotalCost = (costPrice + shippingFee + lateCollectionFee) * item.quantity;
    return sum + itemTotalCost;
  }, 0);

  return totalAmount - totalCost;
}

/**
 * Get fee breakdown for display purposes
 */
export function getFeeBreakdown(
  orderItems: Array<{
    quantity: number;
    shippingFee?: number | null;
    lateCollectionFee?: number | null;
  }>,
  deliveryFee: number,
  isBulkDelivery: boolean,
  feeSettings: FeeSettings
) {
  const totalShoes = orderItems.reduce((sum, item) => sum + item.quantity, 0);
  
  const shippingFees = orderItems.reduce((sum, item) => 
    sum + ((item.shippingFee || 0) * item.quantity), 0);
  
  const lateCollectionFees = orderItems.reduce((sum, item) => 
    sum + ((item.lateCollectionFee || 0) * item.quantity), 0);

  const bulkDiscount = isBulkDelivery ? 
    (feeSettings.defaultDeliveryFee - feeSettings.defaultBulkDeliveryFee) * totalShoes : 0;

  return {
    shippingFees,
    deliveryFee,
    lateCollectionFees,
    bulkDiscount,
    totalFees: shippingFees + deliveryFee + lateCollectionFees,
    totalShoes,
  };
} 