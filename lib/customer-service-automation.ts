import { BUSINESS_CONFIG, MESSAGE_STATUS } from '@/utils/constants';
import { sendEmail } from './email';
import prisma from './prisma';

/**
 * Customer Service Automation System
 * Handles automated responses, smart routing, and customer support
 */

export interface AutoResponse {
  success: boolean;
  message: string;
  shouldEscalate: boolean;
  category: 'order_status' | 'delivery' | 'payment' | 'product' | 'general' | 'lay_buy';
}

/**
 * Analyze customer message and generate automated response
 */
export async function analyzeCustomerMessage(message: string, customerEmail: string): Promise<AutoResponse> {
  const lowerMessage = message.toLowerCase();
  
  // Check for order status inquiries
  if (lowerMessage.includes('order') && (lowerMessage.includes('status') || lowerMessage.includes('where'))) {
    return await handleOrderStatusInquiry(customerEmail);
  }
  
  // Check for delivery inquiries
  if (lowerMessage.includes('delivery') || lowerMessage.includes('delivered') || lowerMessage.includes('tracking')) {
    return await handleDeliveryInquiry(customerEmail);
  }
  
  // Check for payment issues
  if (lowerMessage.includes('payment') || lowerMessage.includes('paid') || lowerMessage.includes('money')) {
    return await handlePaymentInquiry(customerEmail);
  }
  
  // Check for product questions
  if (lowerMessage.includes('product') || lowerMessage.includes('shoe') || lowerMessage.includes('size')) {
    return await handleProductInquiry(lowerMessage);
  }
  
  // Check for lay-buy questions
  if (lowerMessage.includes('lay') && lowerMessage.includes('buy')) {
    return await handleLayBuyInquiry(customerEmail);
  }
  
  // Default response for general inquiries
  return {
    success: true,
    message: generateGeneralResponse(),
    shouldEscalate: false,
    category: 'general',
  };
}

/**
 * Handle order status inquiries
 */
async function handleOrderStatusInquiry(customerEmail: string): Promise<AutoResponse> {
  // Find recent orders for this customer
  const recentOrders = await prisma.order.findMany({
    where: {
      user: { email: customerEmail },
    },
    orderBy: { createdAt: 'desc' },
    take: 3,
    include: {
      orderItems: {
        include: {
          product: true,
        },
      },
    },
  });

  if (recentOrders.length === 0) {
    return {
      success: true,
      message: `Hi! I couldn't find any recent orders for your email address. If you've placed an order recently, please make sure you're using the same email address you used during checkout. You can also check your order status by logging into your account at ${process.env.NEXT_PUBLIC_APP_URL}/orders.`,
      shouldEscalate: false,
      category: 'order_status',
    };
  }

  const latestOrder = recentOrders[0];
  const orderStatus = getOrderStatusMessage(latestOrder.status);
  
  return {
    success: true,
    message: `Hi! I found your most recent order ${latestOrder.orderNumber}. Here's the current status:

📦 Order Status: ${orderStatus}
💰 Total Amount: M${latestOrder.totalAmount}
📅 Ordered: ${latestOrder.createdAt.toLocaleDateString()}

${latestOrder.trackingNumber ? `📋 Tracking Number: ${latestOrder.trackingNumber}` : ''}

If you need more specific information or have questions about this order, please let me know!`,
    shouldEscalate: false,
    category: 'order_status',
  };
}

/**
 * Handle delivery inquiries
 */
async function handleDeliveryInquiry(customerEmail: string): Promise<AutoResponse> {
  // Find recent orders with delivery information
  const recentOrders = await prisma.order.findMany({
    where: {
      user: { email: customerEmail },
      status: { in: ['CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED'] },
    },
    orderBy: { createdAt: 'desc' },
    take: 3,
  });

  if (recentOrders.length === 0) {
    return {
      success: true,
      message: `Hi! I couldn't find any recent orders that are currently being delivered. If you're expecting a delivery, please make sure you're using the same email address you used during checkout. You can also check your order status by logging into your account.`,
      shouldEscalate: false,
      category: 'delivery',
    };
  }

  const latestOrder = recentOrders[0];
  
  if (latestOrder.status === 'DELIVERED') {
    return {
      success: true,
      message: `Great news! Your order ${latestOrder.orderNumber} has been delivered successfully. 

🎉 Delivery Complete!
📅 Delivered: ${latestOrder.deliveredAt?.toLocaleDateString()}

We hope you love your new shoes! If you have any issues with your order, please let us know.`,
      shouldEscalate: false,
      category: 'delivery',
    };
  }

  return {
    success: true,
    message: `Hi! I found your order ${latestOrder.orderNumber} which is currently being processed for delivery.

📦 Current Status: ${getOrderStatusMessage(latestOrder.status)}
📅 Ordered: ${latestOrder.createdAt.toLocaleDateString()}

${latestOrder.trackingNumber ? `📋 Tracking Number: ${latestOrder.trackingNumber}` : ''}

Our delivery partner Delva will contact you when your order is ready for delivery. If you have any specific delivery questions, please let me know!`,
    shouldEscalate: false,
    category: 'delivery',
  };
}

/**
 * Handle payment inquiries
 */
async function handlePaymentInquiry(customerEmail: string): Promise<AutoResponse> {
  return {
    success: true,
    message: `Hi! Here's information about our payment options:

💳 Payment Methods:
• M-Pesa: ${BUSINESS_CONFIG.MPESA_NUMBER} (Katleho Namane)
• EcoCash: ${BUSINESS_CONFIG.ECOCASH_NUMBER} (Katleho Namane)
• Bank Transfer: Contact us for details

📸 Payment Proof:
After making payment, please upload proof via your order page or WhatsApp us at ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}.

⏰ Payment Verification:
Payments are typically verified within 1-2 hours during business hours.

If you have a specific payment issue, please provide your order number and I'll help you further!`,
    shouldEscalate: false,
    category: 'payment',
  };
}

/**
 * Handle product inquiries
 */
async function handleProductInquiry(message: string): Promise<AutoResponse> {
  if (message.includes('size')) {
    return {
      success: true,
      message: `Hi! Here's information about our sizing:

👟 Size Guide:
• We offer sizes 5-12 (UK sizing)
• Most customers find our shoes run true to size
• If you're between sizes, we recommend going up half a size

📏 How to Measure:
1. Place your foot on a piece of paper
2. Mark the longest point (usually the big toe)
3. Measure from heel to toe in centimeters
4. Use our size chart to find your perfect fit

If you need help finding the right size, please let me know your foot measurement and I can recommend the best size for you!`,
      shouldEscalate: false,
      category: 'product',
    };
  }

  return {
    success: true,
    message: `Hi! I'd be happy to help you with product information. 

🛍️ Browse Our Collection:
You can view all our products at ${process.env.NEXT_PUBLIC_APP_URL}/products

📞 Need Specific Help?
If you have questions about a particular product, please let me know:
• Product name or ID
• Specific features you're interested in
• Any concerns you have

I'll get back to you with detailed information!`,
    shouldEscalate: false,
    category: 'product',
  };
}

/**
 * Handle lay-buy inquiries
 */
async function handleLayBuyInquiry(customerEmail: string): Promise<AutoResponse> {
  // Find recent lay-buy orders
  const layBuyOrders = await prisma.layBuyOrder.findMany({
    where: {
      user: { email: customerEmail },
    },
    orderBy: { createdAt: 'desc' },
    take: 3,
  });

  if (layBuyOrders.length === 0) {
    return {
      success: true,
      message: `Hi! Here's information about our Lay-Buy program:

💳 How Lay-Buy Works:
• Pay 60% upfront (M-Pesa, EcoCash, or Bank Transfer)
• Pay the remaining 40% over 6 weeks
• Weekly payments of your choice
• No interest or hidden fees

📅 Payment Schedule:
• Due date: 6 weeks from order date
• Grace period: 1 additional week
• Late collection fee: M10 after grace period

💰 Payment Methods:
• M-Pesa: ${BUSINESS_CONFIG.MPESA_NUMBER}
• EcoCash: ${BUSINESS_CONFIG.ECOCASH_NUMBER}
• Bank Transfer: Contact us for details

To start a Lay-Buy order, simply add items to your cart and select the Lay-Buy option at checkout!`,
      shouldEscalate: false,
      category: 'lay_buy',
    };
  }

  const latestLayBuy = layBuyOrders[0];
  const remainingAmount = latestLayBuy.totalAmount - (latestLayBuy.amountPaid || 0);
  const dueDate = latestLayBuy.dueDate?.toLocaleDateString();
  
  return {
    success: true,
    message: `Hi! I found your Lay-Buy order ${latestLayBuy.orderNumber}. Here's the current status:

💳 Lay-Buy Status: ${latestLayBuy.status}
💰 Total Amount: M${latestLayBuy.totalAmount}
💵 Amount Paid: M${latestLayBuy.amountPaid || 0}
💸 Remaining: M${remainingAmount}
📅 Due Date: ${dueDate}

💳 Payment Methods:
• M-Pesa: ${BUSINESS_CONFIG.MPESA_NUMBER}
• EcoCash: ${BUSINESS_CONFIG.ECOCASH_NUMBER}
• Bank Transfer: Contact us for details

📸 Payment Proof:
After making a payment, please upload proof via your Lay-Buy order page or WhatsApp us at ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}.

If you need help with your payments or have questions, please let me know!`,
    shouldEscalate: false,
    category: 'lay_buy',
  };
}

/**
 * Generate general response
 */
function generateGeneralResponse(): string {
  return `Hi! Thank you for contacting ${BUSINESS_CONFIG.COMPANY_NAME}. 

I'm here to help you with any questions about:
• Order status and tracking
• Delivery information
• Payment options
• Product details
• Lay-Buy program
• Returns and refunds

📞 Quick Support:
• WhatsApp: ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}
• Email: ${BUSINESS_CONFIG.SUPPORT_EMAIL}
• Phone: ${BUSINESS_CONFIG.SUPPORT_PHONE}

Please let me know what specific help you need, and I'll assist you right away!`;
}

/**
 * Get order status message
 */
function getOrderStatusMessage(status: string): string {
  const statusMessages = {
    'PENDING': 'Pending - Awaiting payment verification',
    'PAID': 'Paid - Payment confirmed',
    'CONFIRMED': 'Confirmed - Order being prepared',
    'PROCESSING': 'Processing - Order being prepared for delivery',
    'SHIPPED': 'Shipped - Out for delivery',
    'DELIVERED': 'Delivered - Order completed',
    'CANCELLED': 'Cancelled - Order cancelled',
  };
  
  return statusMessages[status as keyof typeof statusMessages] || status;
}

/**
 * Send automated response to customer
 */
export async function sendAutomatedResponse(
  customerEmail: string,
  customerName: string,
  originalMessage: string,
  autoResponse: AutoResponse
): Promise<void> {
  const template = {
    subject: `Re: Your inquiry - ${BUSINESS_CONFIG.COMPANY_NAME}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3b82f6;">Hi ${customerName}!</h2>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 0; white-space: pre-line;">${autoResponse.message}</p>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">💬 Need More Help?</h3>
          <p>If you need further assistance, please reply to this email or contact us:</p>
          <ul>
            <li><strong>WhatsApp:</strong> ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}</li>
            <li><strong>Email:</strong> ${BUSINESS_CONFIG.SUPPORT_EMAIL}</li>
            <li><strong>Phone:</strong> ${BUSINESS_CONFIG.SUPPORT_PHONE}</li>
          </ul>
        </div>
        
        <p style="color: #6b7280; font-size: 12px;">
          This is an automated response. A human agent will review your inquiry and may follow up if needed.
        </p>
      </div>
    `,
    text: `
Hi ${customerName}!

${autoResponse.message}

Need More Help?
If you need further assistance, please reply to this email or contact us:
• WhatsApp: ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}
• Email: ${BUSINESS_CONFIG.SUPPORT_EMAIL}
• Phone: ${BUSINESS_CONFIG.SUPPORT_PHONE}

This is an automated response. A human agent will review your inquiry and may follow up if needed.
    `,
  };

  await sendEmail({
    to: customerEmail,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

/**
 * Escalate customer inquiry to human agent
 */
export async function escalateCustomerInquiry(
  contactMessageId: string,
  reason: string
): Promise<void> {
  // Update contact message status
  await prisma.contactMessage.update({
    where: { id: contactMessageId },
    data: {
      status: MESSAGE_STATUS.READ,
      adminNotes: `AUTO-ESCALATED: ${reason}`,
    },
  });

  // Send notification to admin
  const template = {
    subject: `🚨 Customer Inquiry Escalated - ${BUSINESS_CONFIG.COMPANY_NAME}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #ef4444;">🚨 Customer Inquiry Escalated</h2>
        
        <div style="background: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Escalation Details</h3>
          <p><strong>Reason:</strong> ${reason}</p>
          <p><strong>Message ID:</strong> ${contactMessageId}</p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Action Required</h3>
          <p>Please review this customer inquiry and respond appropriately:</p>
          <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/contact-messages/${contactMessageId}" 
             style="background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            View Message
          </a>
        </div>
      </div>
    `,
    text: `
Customer Inquiry Escalated

Reason: ${reason}
Message ID: ${contactMessageId}
Time: ${new Date().toLocaleString()}

Please review this customer inquiry and respond appropriately:
${process.env.NEXT_PUBLIC_APP_URL}/admin/contact-messages/${contactMessageId}
    `,
  };

  await sendEmail({
    to: BUSINESS_CONFIG.SUPPORT_EMAIL,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

/**
 * Process incoming customer message with automation
 */
export async function processCustomerMessage(
  contactMessageId: string,
  customerEmail: string,
  customerName: string,
  message: string
): Promise<void> {
  try {
    // Analyze message and generate automated response
    const autoResponse = await analyzeCustomerMessage(message, customerEmail);
    
    // Send automated response
    await sendAutomatedResponse(customerEmail, customerName, message, autoResponse);
    
    // Update contact message with automation info
    await prisma.contactMessage.update({
      where: { id: contactMessageId },
      data: {
        status: autoResponse.shouldEscalate ? MESSAGE_STATUS.READ : MESSAGE_STATUS.REPLIED,
        adminNotes: `AUTO-RESPONDED: Category: ${autoResponse.category}, Escalated: ${autoResponse.shouldEscalate}`,
      },
    });
    
    // Escalate if needed
    if (autoResponse.shouldEscalate) {
      await escalateCustomerInquiry(contactMessageId, `Complex inquiry requiring human attention`);
    }
    
  } catch (error) {
    console.error('Error processing customer message:', error);
    
    // Fallback: escalate to human
    await escalateCustomerInquiry(contactMessageId, `Automation failed: ${error}`);
  }
} 