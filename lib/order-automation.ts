import { BUSINESS_CONFIG, ORDER_STATUS } from '@/utils/constants';
import { scheduleDelvaDelivery } from './delva-service';
import { sendEmail } from './email';
import prisma from './prisma';

/**
 * Order Automation System
 * Handles automated order processing, delivery scheduling, and status updates
 */

/**
 * Automatically process new orders
 * This function should be called when an order is created
 */
export async function processNewOrder(orderId: string): Promise<void> {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        user: true,
        orderItems: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!order) {
      throw new Error('Order not found');
    }

    // Update order status to PAID if payment proof is provided
    const paymentProof = await prisma.paymentProof.findFirst({
      where: { orderId: orderId }
    });
    
    if (paymentProof?.status === 'VERIFIED') {
      await prisma.order.update({
        where: { id: orderId },
        data: { status: ORDER_STATUS.PAID },
      });
    }

    // Schedule delivery with Delva if order is confirmed
    if (order.status === ORDER_STATUS.CONFIRMED || order.status === ORDER_STATUS.PAID) {
      await scheduleOrderDelivery(order);
    }

    // Send order confirmation email
    await sendOrderConfirmationEmail(order);

    // Send admin notification
    await sendAdminOrderNotification(order);

  } catch (error) {
    console.error('Error processing new order:', error);
    throw error;
  }
}

/**
 * Schedule order delivery with Delva
 */
async function scheduleOrderDelivery(order: any): Promise<void> {
  try {
    // Prepare delivery data for Delva
    const deliveryData = {
      orderId: order.id,
      orderNumber: order.orderNumber,
      customerName: order.user.name,
      customerPhone: order.phoneNumber,
      customerEmail: order.user.email,
      deliveryAddress: order.shippingAddress,
      items: order.orderItems.map((item: any) => ({
        name: item.product.name,
        quantity: item.quantity,
        size: item.size,
        color: item.color,
      })),
      totalAmount: order.totalAmount,
      deliveryFee: order.deliveryFee || 0,
      isBulkDelivery: order.isBulkDelivery || false,
      specialInstructions: order.notes,
    };

    // Schedule delivery with Delva
    const delvaResponse = await scheduleDelvaDelivery(deliveryData);

    if (delvaResponse.success) {
      // Update order with tracking information
      await prisma.order.update({
        where: { id: order.id },
        data: {
          trackingNumber: delvaResponse.trackingNumber,
          status: ORDER_STATUS.PROCESSING,
          delvaNotified: true,
          delvaNotifiedAt: new Date(),
        },
      });

      console.log(`Delivery scheduled for order ${order.orderNumber} with tracking ${delvaResponse.trackingNumber}`);
    } else {
      console.error(`Failed to schedule delivery for order ${order.orderNumber}:`, delvaResponse.error);
      
      // Update order to indicate Delva notification failed
      await prisma.order.update({
        where: { id: order.id },
        data: {
          delvaNotified: false,
          adminNotes: `Delva delivery scheduling failed: ${delvaResponse.error}`,
        },
      });
    }
  } catch (error) {
    console.error('Error scheduling order delivery:', error);
    throw error;
  }
}

/**
 * Send order confirmation email to customer
 */
async function sendOrderConfirmationEmail(order: any): Promise<void> {
  const template = {
    subject: `🎉 Order Confirmed - ${order.orderNumber} | ${BUSINESS_CONFIG.COMPANY_NAME}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #10b981;">🎉 Order Confirmed!</h2>
        
        <p>Dear ${order.user.name},</p>
        
        <p>Thank you for your order! Your order <strong>${order.orderNumber}</strong> has been confirmed and is being processed.</p>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📦 Order Details</h3>
          <p><strong>Order Number:</strong> ${order.orderNumber}</p>
          <p><strong>Total Amount:</strong> M${order.totalAmount}</p>
          <p><strong>Delivery Fee:</strong> M${order.deliveryFee || 0}</p>
          <p><strong>Order Date:</strong> ${order.createdAt.toLocaleDateString('en-LS', {
            timeZone: 'Africa/Maseru',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}</p>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📋 Items Ordered</h3>
          <ul>
            ${order.orderItems.map((item: any) => 
              `<li>${item.product.name} - Qty: ${item.quantity}${item.size ? ` - Size: ${item.size}` : ''}${item.color ? ` - Color: ${item.color}` : ''} - M${item.price}</li>`
            ).join('')}
          </ul>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">🚚 Delivery Information</h3>
          <p><strong>Delivery Address:</strong> ${order.shippingAddress}</p>
          <p><strong>Phone Number:</strong> ${order.phoneNumber}</p>
          <p>Our delivery partner Delva will contact you when your order is ready for delivery.</p>
        </div>
        
        <div style="background: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">💬 Need Help?</h3>
          <p>If you have any questions about your order:</p>
          <ul>
            <li><strong>WhatsApp:</strong> ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}</li>
            <li><strong>Email:</strong> ${BUSINESS_CONFIG.SUPPORT_EMAIL}</li>
            <li><strong>Phone:</strong> ${BUSINESS_CONFIG.SUPPORT_PHONE}</li>
          </ul>
        </div>
        
        <p>Thank you for choosing ${BUSINESS_CONFIG.COMPANY_NAME}!</p>
      </div>
    `,
    text: `
Order Confirmed!

Dear ${order.user.name},

Thank you for your order! Your order ${order.orderNumber} has been confirmed and is being processed.

Order Details:
Order Number: ${order.orderNumber}
Total Amount: M${order.totalAmount}
Delivery Fee: M${order.deliveryFee || 0}
Order Date: ${order.createdAt.toLocaleDateString()}

Items Ordered:
${order.orderItems.map((item: any) => 
  `- ${item.product.name} - Qty: ${item.quantity}${item.size ? ` - Size: ${item.size}` : ''}${item.color ? ` - Color: ${item.color}` : ''} - M${item.price}`
).join('\n')}

Delivery Information:
Delivery Address: ${order.shippingAddress}
Phone Number: ${order.phoneNumber}

Our delivery partner Delva will contact you when your order is ready for delivery.

Need Help?
WhatsApp: ${BUSINESS_CONFIG.SUPPORT_WHATSAPP}
Email: ${BUSINESS_CONFIG.SUPPORT_EMAIL}
Phone: ${BUSINESS_CONFIG.SUPPORT_PHONE}

Thank you for choosing ${BUSINESS_CONFIG.COMPANY_NAME}!
    `,
  };

  await sendEmail({
    to: order.user.email,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

/**
 * Send admin notification for new order
 */
async function sendAdminOrderNotification(order: any): Promise<void> {
  const template = {
    subject: `🆕 New Order Received - ${order.orderNumber} | ${BUSINESS_CONFIG.COMPANY_NAME}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3b82f6;">🆕 New Order Received</h2>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📋 Order Information</h3>
          <p><strong>Order Number:</strong> ${order.orderNumber}</p>
          <p><strong>Customer:</strong> ${order.user.name}</p>
          <p><strong>Email:</strong> ${order.user.email}</p>
          <p><strong>Phone:</strong> ${order.phoneNumber}</p>
          <p><strong>Total Amount:</strong> M${order.totalAmount}</p>
          <p><strong>Status:</strong> ${order.status}</p>
          <p><strong>Order Date:</strong> ${order.createdAt.toLocaleDateString()}</p>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📦 Items Ordered</h3>
          <ul>
            ${order.orderItems.map((item: any) => 
              `<li>${item.product.name} - Qty: ${item.quantity}${item.size ? ` - Size: ${item.size}` : ''}${item.color ? ` - Color: ${item.color}` : ''} - M${item.price}</li>`
            ).join('')}
          </ul>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">🚚 Delivery Details</h3>
          <p><strong>Address:</strong> ${order.shippingAddress}</p>
          <p><strong>Delivery Fee:</strong> M${order.deliveryFee || 0}</p>
          <p><strong>Bulk Delivery:</strong> ${order.isBulkDelivery ? 'Yes' : 'No'}</p>
          ${order.notes ? `<p><strong>Notes:</strong> ${order.notes}</p>` : ''}
        </div>
        
        <div style="background: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">⚡ Action Required</h3>
          <p>Please review this order and take necessary actions:</p>
          <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/orders/${order.id}" 
             style="background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            View Order Details
          </a>
        </div>
      </div>
    `,
    text: `
New Order Received

Order Information:
Order Number: ${order.orderNumber}
Customer: ${order.user.name}
Email: ${order.user.email}
Phone: ${order.phoneNumber}
Total Amount: M${order.totalAmount}
Status: ${order.status}
Order Date: ${order.createdAt.toLocaleDateString()}

Items Ordered:
${order.orderItems.map((item: any) => 
  `- ${item.product.name} - Qty: ${item.quantity}${item.size ? ` - Size: ${item.size}` : ''}${item.color ? ` - Color: ${item.color}` : ''} - M${item.price}`
).join('\n')}

Delivery Details:
Address: ${order.shippingAddress}
Delivery Fee: M${order.deliveryFee || 0}
Bulk Delivery: ${order.isBulkDelivery ? 'Yes' : 'No'}
${order.notes ? `Notes: ${order.notes}` : ''}

Action Required:
Please review this order and take necessary actions:
${process.env.NEXT_PUBLIC_APP_URL}/admin/orders/${order.id}
    `,
  };

  await sendEmail({
    to: BUSINESS_CONFIG.SUPPORT_EMAIL,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

/**
 * Automatically update order status based on delivery progress
 */
export async function updateOrderStatusFromDelivery(orderId: string, deliveryStatus: string): Promise<void> {
  try {
    let newOrderStatus: string = ORDER_STATUS.PROCESSING;

    switch (deliveryStatus) {
      case 'PICKED_UP':
        newOrderStatus = ORDER_STATUS.PROCESSING;
        break;
      case 'IN_TRANSIT':
        newOrderStatus = ORDER_STATUS.SHIPPED;
        break;
      case 'OUT_FOR_DELIVERY':
        newOrderStatus = ORDER_STATUS.SHIPPED;
        break;
      case 'DELIVERED':
        newOrderStatus = ORDER_STATUS.DELIVERED;
        break;
      case 'FAILED':
      case 'RETURNED':
        newOrderStatus = ORDER_STATUS.CANCELLED;
        break;
    }

    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: newOrderStatus as any,
        ...(newOrderStatus === ORDER_STATUS.DELIVERED && { deliveredAt: new Date() }),
      },
    });

    console.log(`Order ${orderId} status updated to ${newOrderStatus} based on delivery status ${deliveryStatus}`);
  } catch (error) {
    console.error('Error updating order status from delivery:', error);
    throw error;
  }
}

/**
 * Get orders that need attention (pending payment verification, failed deliveries, etc.)
 */
export async function getOrdersNeedingAttention(): Promise<any[]> {
  const orders = await prisma.order.findMany({
    where: {
      OR: [
        { status: ORDER_STATUS.PENDING },
        { 
          status: ORDER_STATUS.CONFIRMED,
          delvaNotified: false 
        },
        {
          status: ORDER_STATUS.PROCESSING,
          updatedAt: {
            lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Older than 7 days
          }
        }
      ]
    },
    include: {
      user: {
        select: {
          name: true,
          email: true,
        },
      },
      orderItems: {
        include: {
          product: {
            select: {
              name: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  return orders;
}

/**
 * Send daily order summary to admin
 */
export async function sendDailyOrderSummary(): Promise<void> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const orders = await prisma.order.findMany({
    where: {
      createdAt: {
        gte: today,
        lt: tomorrow,
      },
    },
    include: {
      user: {
        select: {
          name: true,
        },
      },
    },
  });

  const summary = {
    totalOrders: orders.length,
    totalRevenue: orders.reduce((sum, order) => sum + order.totalAmount, 0),
    statusBreakdown: orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    orders: orders.slice(0, 10), // Top 10 orders
  };

  const template = {
    subject: `📊 Daily Order Summary - ${today.toLocaleDateString()} | ${BUSINESS_CONFIG.COMPANY_NAME}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3b82f6;">📊 Daily Order Summary</h2>
        <p><strong>Date:</strong> ${today.toLocaleDateString()}</p>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📈 Summary</h3>
          <p><strong>Total Orders:</strong> ${summary.totalOrders}</p>
          <p><strong>Total Revenue:</strong> M${summary.totalRevenue.toFixed(2)}</p>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">📋 Status Breakdown</h3>
          <ul>
            ${Object.entries(summary.statusBreakdown).map(([status, count]) => 
              `<li><strong>${status}:</strong> ${count} orders</li>`
            ).join('')}
          </ul>
        </div>
        
        ${summary.orders.length > 0 ? `
          <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">📦 Recent Orders</h3>
            <ul>
              ${summary.orders.map((order: any) => 
                `<li><strong>${order.orderNumber}</strong> - ${order.user.name} - M${order.totalAmount} - ${order.status}</li>`
              ).join('')}
            </ul>
          </div>
        ` : ''}
        
        <div style="background: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">🔗 Quick Links</h3>
          <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/orders" 
             style="background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">
            View All Orders
          </a>
          <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/dashboard" 
             style="background: #10b981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            Dashboard
          </a>
        </div>
      </div>
    `,
    text: `
Daily Order Summary

Date: ${today.toLocaleDateString()}

Summary:
Total Orders: ${summary.totalOrders}
Total Revenue: M${summary.totalRevenue.toFixed(2)}

Status Breakdown:
${Object.entries(summary.statusBreakdown).map(([status, count]) => 
  `${status}: ${count} orders`
).join('\n')}

${summary.orders.length > 0 ? `
Recent Orders:
${summary.orders.map((order: any) => 
  `${order.orderNumber} - ${order.user.name} - M${order.totalAmount} - ${order.status}`
).join('\n')}
` : ''}

Quick Links:
View All Orders: ${process.env.NEXT_PUBLIC_APP_URL}/admin/orders
Dashboard: ${process.env.NEXT_PUBLIC_APP_URL}/admin/dashboard
    `,
  };

  await sendEmail({
    to: BUSINESS_CONFIG.SUPPORT_EMAIL,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
} 