"use server"

import { getUserById } from "@/actions/userActions";
import { auth } from "@/lib/auth";
import { UserRole } from "@/utils/types";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

/**
 * Get the current session on the server side
 */
export async function getSession() {
  return await auth.api.getSession({
    headers: await headers(),
  });
}

/**
 * Get the current user on the server side
 */
export async function getCurrentUser() {
  const session = await getSession();
  if (!session?.user) return null;
  const user = await getUserById(session.user.id);

  if (!user.success)
  {
    console.error("Error fetching user:", user.error);
    return null;
  }

  return user.data;
}

/**
 * Check if the current user has the required role
 */
export async function hasRole(requiredRole: UserRole): Promise<boolean> {
  const user = await getCurrentUser();
  if (!user) return false;

  // Admin has access to everything
  if (user.role === UserRole.ADMIN) return true;

  // Check specific role
  return user.role === requiredRole;
}

/**
 * Require authentication - redirect to sign-in if not authenticated
 */
export async function requireAuth() {
  const user = await getCurrentUser();
  if (!user) {
    redirect("/sign-in");
  }
  return user;
}

/**
 * Require admin role - redirect to dashboard if not admin
 */
export async function requireAdmin() {
  const user = await requireAuth();
  if (user.role !== UserRole.ADMIN) {
    redirect("/dashboard");
  }
  return user;
}

/**
 * Check if user is admin
 */
export async function isAdmin(): Promise<boolean> {
  const user = await getCurrentUser();
  return user?.role === UserRole.ADMIN || false;
}

/**
 * Get user role
 */
export async function getUserRole(): Promise<UserRole | null> {
  const user = await getCurrentUser();
  return user?.role || null;
}
