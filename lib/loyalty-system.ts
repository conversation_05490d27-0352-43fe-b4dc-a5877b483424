/**
 * Automated Loyalty Discount System
 * Handles automatic generation of loyalty discounts after customer's 5th order
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const LOYALTY_CONFIG = {
  ORDERS_REQUIRED: 5, // Number of completed orders required
  DISCOUNT_AMOUNT: 150, // M150 discount
  DISCOUNT_CODE_PREFIX: 'LOYALTY',
  VALID_DAYS: 90, // Discount valid for 90 days
} as const;

/**
 * Check if customer is eligible for loyalty discount and generate if needed
 */
export async function checkAndGenerateLoyaltyDiscount(userId: string): Promise<{
  success: boolean;
  discountGenerated: boolean;
  discountCode?: string;
  message?: string;
  error?: string;
}> {
  try {
    // Count completed orders for this user (excluding cancelled orders)
    const completedOrdersCount = await prisma.order.count({
      where: {
        userId: userId,
        status: {
          in: ['DELIVERED', 'CONFIRMED'] // Consider both delivered and confirmed as completed
        }
      }
    });

    // Check if user has exactly 5 completed orders (to avoid generating multiple times)
    if (completedOrdersCount !== LOYALTY_CONFIG.ORDERS_REQUIRED) {
      return {
        success: true,
        discountGenerated: false,
        message: `Customer has ${completedOrdersCount} completed orders. Loyalty discount requires ${LOYALTY_CONFIG.ORDERS_REQUIRED} orders.`
      };
    }

    // Check if loyalty discount already exists for this user
    const existingLoyaltyDiscount = await prisma.discountCode.findFirst({
      where: {
        description: {
          startsWith: `LOYALTY:${userId}`
        }
      }
    });

    if (existingLoyaltyDiscount) {
      return {
        success: true,
        discountGenerated: false,
        message: 'Loyalty discount already exists for this customer'
      };
    }

    // Generate unique loyalty discount code
    const discountCode = generateLoyaltyCode(userId);
    
    // Set expiration date
    const validUntil = new Date();
    validUntil.setDate(validUntil.getDate() + LOYALTY_CONFIG.VALID_DAYS);

    // Create the loyalty discount
    const loyaltyDiscount = await prisma.discountCode.create({
      data: {
        code: discountCode,
        description: `LOYALTY:${userId}:5th Order Reward`,
        type: 'FIXED_AMOUNT',
        value: LOYALTY_CONFIG.DISCOUNT_AMOUNT,
        maxUses: 1, // Single use only
        isActive: true,
        validUntil: validUntil,
      }
    });

    // Get user details for notification
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { name: true, email: true }
    });

    // Log the loyalty discount generation
    console.log(`🎉 Loyalty discount generated for user ${userId}: ${discountCode}`);

    return {
      success: true,
      discountGenerated: true,
      discountCode: loyaltyDiscount.code,
      message: `Loyalty discount M${LOYALTY_CONFIG.DISCOUNT_AMOUNT} generated for ${user?.name || 'customer'}`
    };

  } catch (error) {
    console.error('Error checking/generating loyalty discount:', error);
    return {
      success: false,
      discountGenerated: false,
      error: 'Failed to process loyalty discount'
    };
  }
}

/**
 * Generate unique loyalty discount code
 */
function generateLoyaltyCode(userId: string): string {
  const timestamp = Date.now().toString(36).toUpperCase();
  const userSuffix = userId.substring(0, 4).toUpperCase();
  const random = Math.random().toString(36).substring(2, 4).toUpperCase();
  
  return `${LOYALTY_CONFIG.DISCOUNT_CODE_PREFIX}_${userSuffix}_${timestamp}_${random}`;
}

/**
 * Get loyalty discount status for a user
 */
export async function getLoyaltyDiscountStatus(userId: string): Promise<{
  completedOrders: number;
  ordersRemaining: number;
  hasLoyaltyDiscount: boolean;
  loyaltyDiscount?: {
    code: string;
    isActive: boolean;
    validUntil: Date | null;
    isUsed: boolean;
  };
}> {
  try {
    // Count completed orders
    const completedOrdersCount = await prisma.order.count({
      where: {
        userId: userId,
        status: {
          in: ['DELIVERED', 'CONFIRMED']
        }
      }
    });

    // Check for existing loyalty discount
    const loyaltyDiscount = await prisma.discountCode.findFirst({
      where: {
        description: {
          startsWith: `LOYALTY:${userId}`
        }
      }
    });

    const ordersRemaining = Math.max(0, LOYALTY_CONFIG.ORDERS_REQUIRED - completedOrdersCount);

    return {
      completedOrders: completedOrdersCount,
      ordersRemaining,
      hasLoyaltyDiscount: !!loyaltyDiscount,
      loyaltyDiscount: loyaltyDiscount ? {
        code: loyaltyDiscount.code,
        isActive: loyaltyDiscount.isActive,
        validUntil: loyaltyDiscount.validUntil,
        isUsed: loyaltyDiscount.usedCount > 0
      } : undefined
    };

  } catch (error) {
    console.error('Error getting loyalty discount status:', error);
    return {
      completedOrders: 0,
      ordersRemaining: LOYALTY_CONFIG.ORDERS_REQUIRED,
      hasLoyaltyDiscount: false
    };
  }
}

/**
 * Send loyalty discount notification email to customer
 */
export async function sendLoyaltyDiscountNotification(
  userId: string, 
  discountCode: string
): Promise<void> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { name: true, email: true }
    });

    if (!user?.email) {
      console.log('No email found for user, skipping loyalty notification');
      return;
    }

    // Here you would integrate with your email service
    // For now, we'll just log it
    console.log(`📧 Loyalty discount notification should be sent to ${user.email}:`);
    console.log(`   Discount Code: ${discountCode}`);
    console.log(`   Amount: M${LOYALTY_CONFIG.DISCOUNT_AMOUNT}`);
    console.log(`   Valid for: ${LOYALTY_CONFIG.VALID_DAYS} days`);

    // TODO: Integrate with actual email service
    // await sendEmail({
    //   to: user.email,
    //   subject: '🎉 Congratulations! You\'ve earned a loyalty discount!',
    //   template: 'loyalty-discount',
    //   data: {
    //     customerName: user.name,
    //     discountCode,
    //     discountAmount: LOYALTY_CONFIG.DISCOUNT_AMOUNT,
    //     validDays: LOYALTY_CONFIG.VALID_DAYS
    //   }
    // });

  } catch (error) {
    console.error('Error sending loyalty discount notification:', error);
  }
}

/**
 * Check all recent orders for loyalty discount eligibility
 * This can be called periodically or after order status updates
 */
export async function processLoyaltyDiscountsForRecentOrders(): Promise<{
  processed: number;
  generated: number;
  errors: number;
}> {
  try {
    // Get users who recently had orders delivered/confirmed
    const recentCompletedOrders = await prisma.order.findMany({
      where: {
        status: {
          in: ['DELIVERED', 'CONFIRMED']
        },
        updatedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      select: {
        userId: true
      },
      distinct: ['userId']
    });

    let processed = 0;
    let generated = 0;
    let errors = 0;

    for (const order of recentCompletedOrders) {
      try {
        const result = await checkAndGenerateLoyaltyDiscount(order.userId);
        processed++;
        
        if (result.discountGenerated) {
          generated++;
          // Send notification email
          await sendLoyaltyDiscountNotification(order.userId, result.discountCode!);
        }
        
        if (!result.success) {
          errors++;
        }
      } catch (error) {
        console.error(`Error processing loyalty discount for user ${order.userId}:`, error);
        errors++;
      }
    }

    console.log(`Loyalty discount processing complete: ${processed} processed, ${generated} generated, ${errors} errors`);

    return { processed, generated, errors };

  } catch (error) {
    console.error('Error processing loyalty discounts for recent orders:', error);
    return { processed: 0, generated: 0, errors: 1 };
  }
}
