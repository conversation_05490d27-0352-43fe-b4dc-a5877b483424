import { Product, ShoeType } from "@/utils/types";

/**
 * Convert new Product type to legacy ShoeType for backward compatibility
 */
export function productToShoeType(product: Product): ShoeType {
  return {
    id: product.id,
    name: product.name,
    image: product.images[0] || "", // Use first image
    discountedPrice: product.discountedPrice || product.price,
    price: product.price,
    discount: !!product.discountedPrice,
    rating: product.rating,
    numberOfRatings: product.reviewCount,
  };
}

/**
 * Convert array of products to legacy ShoeType array
 */
export function productsToShoeTypes(products: Product[]): ShoeType[] {
  return products.map(productToShoeType);
}

/**
 * Calculate discount percentage
 */
export function calculateDiscountPercentage(originalPrice: number, discountedPrice: number): number {
  if (discountedPrice >= originalPrice) return 0;
  return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
}

/**
 * Format price with currency
 */
export function formatPrice(price: number, currency: string = "M"): string {
  return `${currency} ${price.toFixed(2)}`;
}

/**
 * Check if product is on sale
 */
export function isProductOnSale(product: Product): boolean {
  return !!product.discountedPrice && product.discountedPrice < product.price;
}

/**
 * Get effective price (discounted price if available, otherwise regular price)
 */
export function getEffectivePrice(product: Product): number {
  return product.discountedPrice || product.price;
}

/**
 * Get available sizes for filtering
 */
export function getAvailableSizes(products: Product[]): string[] {
  const sizes = new Set<string>();
  products.forEach(product => {
    product.sizes.forEach(size => sizes.add(size));
  });
  return Array.from(sizes).sort();
}

/**
 * Get available colors for filtering
 */
export function getAvailableColors(products: Product[]): string[] {
  const colors = new Set<string>();
  products.forEach(product => {
    product.colors.forEach(color => colors.add(color));
  });
  return Array.from(colors).sort();
}

/**
 * Lesotho districts and delivery configuration
 */
export const LESOTHO_DISTRICTS = [
  'Maseru',
  'Berea',
  'Leribe',
  'Mohale\'s Hoek',
  'Mafeteng',
  'Qacha\'s Nek',
  'Thaba-Tseka',
  'Mokhotlong',
  'Quthing',
  'Butha-Buthe',
  'South Africa'
] as const;

export type LesothoDistrict = typeof LESOTHO_DISTRICTS[number];

/**
 * Delivery fee configuration for Lesotho districts
 */
export const DELIVERY_FEES = {
  'Maseru': 0,
  'Berea': 20,
  'Leribe': 30,
  'Mohale\'s Hoek': 40,
  'Mafeteng': 30,
  'Qacha\'s Nek': 50,
  'Thaba-Tseka': 60,
  'Mokhotlong': 60,
  'Quthing': 45,
  'Butha-Buthe': 35,
  'South Africa': 0 // Contact for negotiations
} as const;

/**
 * Calculate delivery fee based on district
 */
export function calculateDeliveryFee(district: string, orderAmount: number): {
  fee: number;
  isFree: boolean;
  reason: string;
} {
  const normalizedDistrict = district.trim();
  
  // Free delivery for orders over M3500 regardless of district
  if (orderAmount >= 3500) {
    return {
      fee: 0,
      isFree: true,
      reason: 'Free delivery for orders over M 3500.00'
    };
  }
  
  // Check if district exists in our fee list
  if (normalizedDistrict in DELIVERY_FEES) {
    const fee = DELIVERY_FEES[normalizedDistrict as keyof typeof DELIVERY_FEES];
    
    if (normalizedDistrict === 'Maseru') {
      return {
        fee: 0,
        isFree: true,
        reason: 'Free delivery in Maseru district'
      };
    }
    
    if (normalizedDistrict === 'South Africa') {
      return {
        fee: 0,
        isFree: false,
        reason: 'Contact +266 62844473 for delivery negotiations'
      };
    }
    
    return {
      fee,
      isFree: false,
      reason: `Delivery fee for ${normalizedDistrict} district`
    };
  }
  
  // Default case for unknown districts
  return {
    fee: 50, // Default fee for unknown districts
    isFree: false,
    reason: 'Standard delivery fee'
  };
}

/**
 * Validate Lesotho phone number format
 * Lesotho phone numbers: +266 followed by 8 digits, or just 8 digits starting with 2, 5, or 6
 */
export function validateLesothoPhoneNumber(phone: string): {
  isValid: boolean;
  error?: string;
  formatted?: string;
} {
  // Remove all spaces, dashes, and parentheses
  const cleaned = phone.replace(/[\s\-\(\)]/g, '');

  // Check for international format: +266xxxxxxxx
  const internationalMatch = cleaned.match(/^\+266([2-9]\d{7})$/);
  if (internationalMatch) {
    return {
      isValid: true,
      formatted: `+266 ${internationalMatch[1].substring(0, 4)} ${internationalMatch[1].substring(4)}`
    };
  }

  // Check for local format: 8 digits starting with 2, 5, or 6
  const localMatch = cleaned.match(/^([256]\d{7})$/);
  if (localMatch) {
    return {
      isValid: true,
      formatted: `+266 ${localMatch[1].substring(0, 4)} ${localMatch[1].substring(4)}`
    };
  }

  return {
    isValid: false,
    error: 'Please enter a valid Lesotho WhatsApp number (e.g., +266 5316 3354 or 53163354)'
  };
}

/**
 * Check if country is restricted (only Lesotho allowed)
 */
export function validateCountryRestriction(country: string): {
  isValid: boolean;
  error?: string;
} {
  if (country !== 'Lesotho') {
    return {
      isValid: false,
      error: 'We currently only deliver within Lesotho'
    };
  }

  return { isValid: true };
}

/**
 * Get available brands for filtering
 */
export function getAvailableBrands(products: Product[]): string[] {
  const brands = new Set<string>();
  products.forEach(product => {
    brands.add(product.brand);
  });
  return Array.from(brands).sort();
}

/**
 * Get price range for filtering
 */
export function getPriceRange(products: Product[]): { min: number; max: number } {
  if (products.length === 0) return { min: 0, max: 0 };
  
  const prices = products.map(getEffectivePrice);
  return {
    min: Math.min(...prices),
    max: Math.max(...prices),
  };
}

/**
 * Validate product data
 */
export function validateProductData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.name || data.name.trim().length === 0) {
    errors.push("Product name is required");
  }

  if (!data.price || isNaN(parseFloat(data.price)) || parseFloat(data.price) <= 0) {
    errors.push("Valid price is required");
  }

  if (data.discountedPrice && (isNaN(parseFloat(data.discountedPrice)) || parseFloat(data.discountedPrice) < 0)) {
    errors.push("Discounted price must be a valid positive number");
  }

  if (data.discountedPrice && parseFloat(data.discountedPrice) >= parseFloat(data.price)) {
    errors.push("Discounted price must be less than regular price");
  }

  if (!data.brand || data.brand.trim().length === 0) {
    errors.push("Brand is required");
  }

  if (!data.categoryId || data.categoryId.trim().length === 0) {
    errors.push("Category is required");
  }

  if (data.stock && (isNaN(parseInt(data.stock)) || parseInt(data.stock) < 0)) {
    errors.push("Stock must be a valid non-negative number");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Calculate the final order total (used in checkout, review, payment, etc.)
 * Always uses positive discountAmount and adds deliveryCost.
 */
export function getFinalOrderTotal(subtotal: number, discountAmount: number, deliveryCost: number): number {
  return subtotal - Math.abs(discountAmount) + deliveryCost;
}
