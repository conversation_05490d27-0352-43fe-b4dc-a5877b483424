import { z } from 'zod';
import { VALIDATION_LIMITS } from '@/utils/constants';

// Contact Form Schema
export const contactFormSchema = z.object({
  name: z.string()
    .min(VALIDATION_LIMITS.NAME_MIN_LENGTH, `Name must be at least ${VALIDATION_LIMITS.NAME_MIN_LENGTH} characters`)
    .max(VALIDATION_LIMITS.NAME_MAX_LENGTH, `Name must not exceed ${VALIDATION_LIMITS.NAME_MAX_LENGTH} characters`)
    .trim(),
  email: z.string()
    .email('Please enter a valid email address')
    .toLowerCase()
    .trim(),
  subject: z.string()
    .min(1, 'Subject is required')
    .max(100, 'Subject must not exceed 100 characters')
    .trim(),
  message: z.string()
    .min(VALIDATION_LIMITS.MESSAGE_MIN_LENGTH, `Message must be at least ${VALIDATION_LIMITS.MESSAGE_MIN_LENGTH} characters`)
    .max(VALIDATION_LIMITS.MESSAGE_MAX_LENGTH, `Message must not exceed ${VALIDATION_LIMITS.MESSAGE_MAX_LENGTH} characters`)
    .trim(),
});

// Order Creation Schema
export const orderCreationSchema = z.object({
  shippingAddress: z.string()
    .min(VALIDATION_LIMITS.ADDRESS_MIN_LENGTH, `Address must be at least ${VALIDATION_LIMITS.ADDRESS_MIN_LENGTH} characters`)
    .max(VALIDATION_LIMITS.ADDRESS_MAX_LENGTH, `Address must not exceed ${VALIDATION_LIMITS.ADDRESS_MAX_LENGTH} characters`)
    .trim(),
  phoneNumber: z.string()
    .min(VALIDATION_LIMITS.PHONE_MIN_LENGTH, `Phone number must be at least ${VALIDATION_LIMITS.PHONE_MIN_LENGTH} digits`)
    .max(VALIDATION_LIMITS.PHONE_MAX_LENGTH, `Phone number must not exceed ${VALIDATION_LIMITS.PHONE_MAX_LENGTH} digits`)
    .trim(),
  notes: z.string().optional(),
  discountAmount: z.number().min(0).optional(),
  discountCode: z.string().optional(),
  paymentMethod: z.string().min(1, 'Payment method is required'),
  paymentProofUrl: z.string().url('Payment proof URL must be valid').optional(),
  deliveryFee: z.number().min(0).optional(),
  items: z.array(z.object({
    productId: z.string().min(1, 'Product ID is required'),
    quantity: z.number().int().positive('Quantity must be a positive integer'),
    price: z.number().positive('Price must be positive'),
    size: z.string().optional(),
  })).min(1, 'At least one item is required'),
});

// Lay-Buy Order Schema
export const layBuyOrderSchema = orderCreationSchema.extend({
  layBuy: z.object({
    totalAmount: z.number().positive('Total amount must be positive'),
    upfrontAmount: z.number().positive('Upfront amount must be positive'),
    remainingAmount: z.number().min(0, 'Remaining amount must be non-negative'),
  }),
});

// Order Status Update Schema
export const orderStatusUpdateSchema = z.object({
  status: z.enum(['PENDING', 'PAID', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED']),
  trackingNumber: z.string().optional(),
  trackingUrl: z.string().url().optional(),
  adminNotes: z.string().optional(),
});

// Product Creation/Update Schema
export const productSchema = z.object({
  name: z.string()
    .min(1, 'Product name is required')
    .max(100, 'Product name must not exceed 100 characters')
    .trim(),
  description: z.string().optional(),
  price: z.number().positive('Price must be positive'),
  discountedPrice: z.number().positive().optional(),
  brand: z.string()
    .min(1, 'Brand is required')
    .max(50, 'Brand must not exceed 50 characters')
    .trim(),
  categoryId: z.string().min(1, 'Category is required'),
  stock: z.number().int().min(0, 'Stock must be non-negative'),
  isActive: z.boolean().optional(),
  images: z.array(z.string().url()).min(1, 'At least one image is required'),
  sizes: z.array(z.string()).min(1, 'At least one size is required'),
  costPrice: z.number().positive().optional(),
  shippingFee: z.number().min(0).optional(),
  lateCollectionFee: z.number().min(0).optional(),
});

// User Registration Schema
export const userRegistrationSchema = z.object({
  name: z.string()
    .min(VALIDATION_LIMITS.NAME_MIN_LENGTH, `Name must be at least ${VALIDATION_LIMITS.NAME_MIN_LENGTH} characters`)
    .max(VALIDATION_LIMITS.NAME_MAX_LENGTH, `Name must not exceed ${VALIDATION_LIMITS.NAME_MAX_LENGTH} characters`)
    .trim(),
  email: z.string()
    .email('Please enter a valid email address')
    .toLowerCase()
    .trim(),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(100, 'Password must not exceed 100 characters'),
});

// Password Reset Schema
export const passwordResetSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .toLowerCase()
    .trim(),
});

// Payment Proof Schema
export const paymentProofSchema = z.object({
  imageUrl: z.string().url('Payment proof URL must be valid'),
  notes: z.string().optional(),
});

// Admin Settings Schema
export const adminSettingsSchema = z.object({
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Primary color must be a valid hex color'),
  defaultShippingFee: z.number().min(0, 'Shipping fee must be non-negative'),
  defaultLateCollectionFee: z.number().min(0, 'Late collection fee must be non-negative'),
  defaultDeliveryFee: z.number().min(0, 'Delivery fee must be non-negative'),
  defaultBulkDeliveryFee: z.number().min(0, 'Bulk delivery fee must be non-negative'),
  bulkDeliveryThreshold: z.number().int().min(1, 'Bulk delivery threshold must be at least 1'),
}); 