/**
 * Lay-Buy System Utilities
 * Helper functions for calculating payments, dates, and managing Lay-Buy orders
 */

export const LAY_BUY_CONFIG = {
  UPFRONT_PERCENTAGE: 0.4, // 40%
  PAYMENT_PERIOD_WEEKS: 6,
  GRACE_PERIOD_WEEKS: 1,
  // EARLY_CANCELLATION_REFUND_PERCENTAGE: 0.5, // 50% (removed, now always 100%)
  MINIMUM_ORDER_AMOUNT: 700, // M700 minimum for Lay-Buy
} as const;

/**
 * Calculate Lay-Buy payment amounts
 */
export function calculateLayBuyAmounts(totalAmount: number) {
  const upfrontAmount = totalAmount * LAY_BUY_CONFIG.UPFRONT_PERCENTAGE;
  const remainingAmount = totalAmount - upfrontAmount;
  
  return {
    totalAmount,
    upfrontAmount: Math.round(upfrontAmount * 100) / 100, // Round to 2 decimal places
    remainingAmount: Math.round(remainingAmount * 100) / 100,
    upfrontPercentage: LAY_BUY_CONFIG.UPFRONT_PERCENTAGE * 100,
    remainingPercentage: (1 - LAY_BUY_CONFIG.UPFRONT_PERCENTAGE) * 100,
  };
}

/**
 * Calculate important dates for a Lay-Buy order
 */
export function calculateLayBuyDates(startDate: Date = new Date()) {
  const dueDate = new Date(startDate);
  dueDate.setDate(dueDate.getDate() + (LAY_BUY_CONFIG.PAYMENT_PERIOD_WEEKS * 7));
  
  const gracePeriodEnd = new Date(dueDate);
  gracePeriodEnd.setDate(gracePeriodEnd.getDate() + (LAY_BUY_CONFIG.GRACE_PERIOD_WEEKS * 7));
  
  return {
    startDate,
    dueDate,
    gracePeriodEnd,
    paymentPeriodWeeks: LAY_BUY_CONFIG.PAYMENT_PERIOD_WEEKS,
    gracePeriodWeeks: LAY_BUY_CONFIG.GRACE_PERIOD_WEEKS,
  };
}

/**
 * Calculate days remaining until due date or grace period end
 */
export function calculateDaysRemaining(dueDate: Date, gracePeriodEnd: Date, currentDate: Date = new Date()) {
  const dueDateMs = dueDate.getTime();
  const gracePeriodEndMs = gracePeriodEnd.getTime();
  const currentMs = currentDate.getTime();
  
  if (currentMs <= dueDateMs) {
    // Still within payment period
    const daysUntilDue = Math.ceil((dueDateMs - currentMs) / (1000 * 60 * 60 * 24));
    return {
      daysRemaining: daysUntilDue,
      isInGracePeriod: false,
      isOverdue: false,
      status: 'active' as const,
    };
  } else if (currentMs <= gracePeriodEndMs) {
    // In grace period
    const daysUntilForfeiture = Math.ceil((gracePeriodEndMs - currentMs) / (1000 * 60 * 60 * 24));
    return {
      daysRemaining: daysUntilForfeiture,
      isInGracePeriod: true,
      isOverdue: true,
      status: 'grace_period' as const,
    };
  } else {
    // Past grace period
    return {
      daysRemaining: 0,
      isInGracePeriod: false,
      isOverdue: true,
      status: 'forfeited' as const,
    };
  }
}

/**
 * Calculate refund amount based on cancellation timing
 */
export function calculateRefundAmount(
  upfrontAmount: number,
  dueDate: Date,
  currentDate: Date = new Date()
): { refundAmount: number; refundPercentage: number; isEligible: boolean } {
  const isBeforeDueDate = currentDate.getTime() <= dueDate.getTime();
  
  if (isBeforeDueDate) {
    // Early cancellation - 100% refund
    const refundAmount = upfrontAmount; // Full refund
    return {
      refundAmount: Math.round(refundAmount * 100) / 100,
      refundPercentage: 100,
      isEligible: true,
    };
  } else {
    // Late cancellation - no refund
    return {
      refundAmount: 0,
      refundPercentage: 0,
      isEligible: false,
    };
  }
}

/**
 * Generate a unique Lay-Buy order number
 */
export function generateLayBuyOrderNumber(): string {
  const timestamp = Date.now().toString(36).toUpperCase();
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `LB-${timestamp}-${random}`;
}

/**
 * Check if an order amount is eligible for Lay-Buy
 */
export function isEligibleForLayBuy(amount: number): boolean {
  return amount >= LAY_BUY_CONFIG.MINIMUM_ORDER_AMOUNT;
}

/**
 * Calculate which week number a reminder should be sent
 */
export function calculateReminderWeek(startDate: Date, currentDate: Date = new Date()): number {
  const diffMs = currentDate.getTime() - startDate.getTime();
  const diffWeeks = Math.floor(diffMs / (1000 * 60 * 60 * 24 * 7));
  return Math.max(1, diffWeeks + 1); // Week 1 is the first week after order
}

/**
 * Get reminder urgency level based on week number
 */
export function getReminderUrgency(weekNumber: number): {
  urgency: 'normal' | 'urgent' | 'critical';
  type: 'WEEKLY' | 'URGENT' | 'GRACE_PERIOD' | 'FINAL_NOTICE';
} {
  if (weekNumber <= 5) {
    return { urgency: 'normal', type: 'WEEKLY' };
  } else if (weekNumber === 6) {
    return { urgency: 'urgent', type: 'URGENT' };
  } else if (weekNumber === 7) {
    return { urgency: 'critical', type: 'GRACE_PERIOD' };
  } else {
    return { urgency: 'critical', type: 'FINAL_NOTICE' };
  }
}

/**
 * Format currency for display
 */
export function formatLayBuyPrice(amount: number): string {
  return `M${amount.toFixed(2)}`;
}

/**
 * Calculate progress percentage for payment completion
 */
export function calculatePaymentProgress(amountPaid: number, totalAmount: number): number {
  const progress = (amountPaid / totalAmount) * 100;
  return Math.min(100, Math.max(0, progress));
}
