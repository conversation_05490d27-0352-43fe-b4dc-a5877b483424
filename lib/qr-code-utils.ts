import QRCode from 'qrcode';

export interface QRCodeOptions {
  width?: number;
  height?: number;
  margin?: number;
  color?: {
    dark?: string;
    light?: string;
  };
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
}

export interface PartnerQRData {
  referralCode: string;
  discountCode: string;
  discountAmount: number;
  partnerName: string;
}

/**
 * Generate QR code as data URL
 */
export async function generateQRCode(
  data: string,
  options: QRCodeOptions = {}
): Promise<string> {
  const defaultOptions = {
    width: 512, // Increased from 256 for better readability
    height: 512, // Increased from 256 for better readability
    margin: 4, // Increased margin for better scanning
    color: {
      dark: '#000000', // Pure black for maximum contrast
      light: '#FFFFFF' // Pure white for maximum contrast
    },
    errorCorrectionLevel: 'H' as const, // High error correction for better scanning
    ...options
  };

  try {
    const dataUrl = await QRCode.toDataURL(data, defaultOptions);
    return dataUrl;
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
}

/**
 * Generate QR code for partner referral
 */
export async function generatePartnerQRCode(
  partnerData: PartnerQRData,
  options: QRCodeOptions = {}
): Promise<string> {
  const url = new URL('https://rivvsneakers.shop');
  url.searchParams.set('ref', partnerData.referralCode);
  url.searchParams.set('discount', partnerData.discountCode);
  
  const qrData = url.toString();
  
  // Use optimized settings for partner QR codes
  const optimizedOptions = {
    width: 512,
    height: 512,
    margin: 4,
    color: {
      dark: '#000000', // Pure black for maximum contrast
      light: '#FFFFFF' // Pure white for maximum contrast
    },
    errorCorrectionLevel: 'H' as const, // High error correction for better scanning
    ...options
  };
  
  return generateQRCode(qrData, optimizedOptions);
}

/**
 * Download QR code as PNG file
 */
export async function downloadQRCode(
  data: string,
  filename: string = 'qr-code.png',
  options: QRCodeOptions = {}
): Promise<void> {
  try {
    // Only include supported options for QRCode.toCanvas
    const canvasOptions: any = {
      margin: options.margin || 4,
      color: options.color || {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: options.errorCorrectionLevel || 'H'
    };
    if (options.width) canvasOptions.width = options.width;
    // height is not a supported option for QRCode.toCanvas

    // Create a canvas element
    const canvas = document.createElement('canvas');
    await QRCode.toCanvas(canvas, data, canvasOptions);

    // Convert canvas to blob with high quality
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
    }, 'image/png', 1.0); // Maximum quality
  } catch (error) {
    console.error('Error downloading QR code:', error);
    throw new Error('Failed to download QR code');
  }
}

/**
 * Download partner QR code with custom filename
 */
export async function downloadPartnerQRCode(
  partnerData: PartnerQRData,
  options: QRCodeOptions = {}
): Promise<void> {
  const url = new URL('https://rivvsneakers.shop');
  url.searchParams.set('ref', partnerData.referralCode);
  url.searchParams.set('discount', partnerData.discountCode);
  
  const filename = `qr-${partnerData.referralCode}-${partnerData.discountCode}.png`;
  await downloadQRCode(url.toString(), filename, options);
}

/**
 * Generate a test QR code optimized for scanning
 */
export async function generateTestQRCode(
  data: string = 'https://rivvsneakers.shop/?ref=TEST123&discount=SAVE10',
  options: QRCodeOptions = {}
): Promise<string> {
  const testOptions = {
    width: 512,
    height: 512,
    margin: 4,
    color: {
      dark: '#000000', // Pure black for maximum contrast
      light: '#FFFFFF' // Pure white for maximum contrast
    },
    errorCorrectionLevel: 'H' as const, // High error correction for better scanning
    ...options
  };
  
  return generateQRCode(data, testOptions);
}

/**
 * Generate QR code with custom styling for different use cases
 */
export async function generateStyledQRCode(
  data: string,
  style: 'default' | 'branded' | 'minimal' = 'default',
  options: QRCodeOptions = {}
): Promise<string> {
  const styleOptions = {
    default: {
      color: { dark: '#000000', light: '#FFFFFF' },
      errorCorrectionLevel: 'M' as const
    },
    branded: {
      color: { dark: '#1E40AF', light: '#FFFFFF' }, // Blue brand color
      errorCorrectionLevel: 'H' as const
    },
    minimal: {
      color: { dark: '#374151', light: '#F9FAFB' }, // Gray tones
      errorCorrectionLevel: 'L' as const
    }
  };

  const mergedOptions = {
    ...styleOptions[style],
    ...options
  };

  return generateQRCode(data, mergedOptions);
} 