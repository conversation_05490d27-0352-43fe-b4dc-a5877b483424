#!/usr/bin/env node

/**
 * Test API Quota and Connectivity
 * Checks if the Google Custom Search API is working and has quota
 */

require('dotenv').config();
const axios = require('axios');

async function testAPIQuota() {
  console.log('🔍 Testing API Quota and Connectivity');
  console.log('====================================\n');

  const apiKey = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY;
  const searchEngineId = process.env.GOOGLE_CUSTOM_SEARCH_ENGINE_ID;

  if (!apiKey || !searchEngineId) {
    console.log('❌ Missing API credentials in .env file');
    console.log(`   API Key: ${apiKey ? 'Present' : 'Missing'}`);
    console.log(`   Search Engine ID: ${searchEngineId ? 'Present' : 'Missing'}`);
    return;
  }

  console.log('✅ API credentials found');
  console.log(`   API Key: ${apiKey.substring(0, 10)}...`);
  console.log(`   Search Engine ID: ${searchEngineId}`);
  console.log('');

  // Test with a simple search query
  const testQuery = 'Nike Air Force 1 white background studio shot';
  const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(testQuery)}&searchType=image&num=3&imgSize=large&imgType=photo&safe=active&imgColorType=color&imgDominantColor=white`;

  try {
    console.log('🔍 Testing search query...');
    console.log(`   Query: "${testQuery}"`);
    console.log('');

    const response = await axios.get(searchUrl);
    
    if (response.data.items && response.data.items.length > 0) {
      console.log('✅ API Test Successful!');
      console.log(`   Found: ${response.data.items.length} images`);
      console.log('');
      
      console.log('📸 Sample Results:');
      response.data.items.forEach((item, index) => {
        console.log(`${index + 1}. ${item.title}`);
        console.log(`   URL: ${item.link.substring(0, 60)}...`);
        console.log(`   Size: ${item.image?.width || 'unknown'}x${item.image?.height || 'unknown'}`);
        console.log('');
      });

      // Check quota information
      if (response.data.searchInformation) {
        console.log('📊 Search Information:');
        console.log(`   Total Results: ${response.data.searchInformation.totalResults}`);
        console.log(`   Search Time: ${response.data.searchInformation.searchTime}s`);
      }

      console.log('🎉 Your Google Custom Search API is working perfectly!');
      console.log('   The 401 error is definitely an authentication issue, not quota.');

    } else {
      console.log('⚠️  API responded but no images found');
      console.log('   This might indicate quota issues or search configuration problems');
    }

  } catch (error) {
    console.error('❌ API Test Failed:');
    
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error: ${error.response.data?.error?.message || 'Unknown error'}`);
      
      if (error.response.status === 403) {
        console.log('');
        console.log('🚨 Quota/Permission Issue Detected:');
        console.log('   • Check if Custom Search API is enabled in Google Cloud Console');
        console.log('   • Verify you have remaining quota for today');
        console.log('   • Ensure billing is enabled if using paid tier');
      } else if (error.response.status === 400) {
        console.log('');
        console.log('🚨 Configuration Issue:');
        console.log('   • Check if Search Engine ID is correct');
        console.log('   • Verify API key has proper permissions');
      }
    } else {
      console.log(`   Network Error: ${error.message}`);
    }
  }

  console.log('\n🔧 Troubleshooting the 401 Error:');
  console.log('==================================');
  console.log('The 401 error in your enhancement script is NOT related to Google API quota.');
  console.log('It\'s an authentication issue with your Next.js API endpoint.');
  console.log('');
  console.log('Solutions:');
  console.log('1. 🎯 Use the admin panel directly (recommended)');
  console.log('   • Go to: http://localhost:3000/admin/products');
  console.log('   • Find a product and click "Enhance Images"');
  console.log('');
  console.log('2. 🔧 Modify the API to allow server-side calls');
  console.log('3. 🧪 Create a direct enhancement script (bypassing API)');
}

testAPIQuota().catch(console.error);
