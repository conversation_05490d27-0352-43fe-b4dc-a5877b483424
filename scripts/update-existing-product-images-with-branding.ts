import prisma from '../lib/prisma';
import { processProductImages } from '../utils/image-ai-utils';

async function main() {
  const products = await prisma.product.findMany();
  console.log(`Found ${products.length} products.`);

  for (const product of products) {
    if (!product.images || product.images.length === 0) {
      console.log(`Skipping product ${product.id} (${product.name}) - no images.`);
      continue;
    }
    const originalImageUrl = product.images[0];
    try {
      console.log(`Processing product ${product.id} (${product.name})...`);
      const processed = await processProductImages({
        productName: product.name,
        brand: product.brand,
        colorway: '', // Optionally extract from description or add logic
        originalImageUrl
      });
      if (processed.images && processed.images.length > 0) {
        await prisma.product.update({
          where: { id: product.id },
          data: {
            images: processed.images.map(i => i.url)
          }
        });
        console.log(`✅ Updated product ${product.id} with ${processed.images.length} branded images.`);
      } else {
        console.log(`⚠️ No enhanced images found for product ${product.id}.`);
      }
    } catch (err) {
      console.error(`❌ Error processing product ${product.id}:`, err);
    }
  }
  console.log('Done.');
}

main().catch((err) => {
  console.error('Fatal error:', err);
  process.exit(1);
}); 